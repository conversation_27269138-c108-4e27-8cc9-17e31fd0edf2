# 语音阅读平台使用指南

## 🎯 项目概述

这是一个现代化的语音阅读平台，类似于 Speechify，提供智能文本转语音功能，支持实时高亮、翻译和书签等功能。

## ✨ 主要功能

### 📚 书籍管理

- **文件上传**: 支持 TXT 格式文件上传（最大 10MB）
- **书籍库**: 显示已上传的所有书籍，包括阅读进度
- **搜索功能**: 按书名或文件名搜索书籍
- **排序选项**: 按最近阅读、书名或上传时间排序

### 🔊 语音朗读

- **多TTS提供商**: 支持浏览器内置 + Microsoft Edge TTS
- **智能语音选择**: 自动检测文本语言并选择最佳语音
- **高质量合成**: Edge TTS 提供更自然的语音效果
- **语速控制**: 0.5x - 2.0x 可调节语速
- **音量控制**: 精确的音量调节
- **语音切换**: 用户可手动选择不同的语音和提供商

### 🎨 实时高亮

- **段落高亮**: 当前朗读段落的视觉突出显示
- **句子高亮**: 当前朗读句子的动态高亮
- **单词高亮**: 精确到单词级别的实时跟踪
- **流畅动画**: 平滑的过渡效果和视觉反馈

### 🌐 翻译功能

- **实时翻译**: 当前段落的即时翻译
- **多语言支持**: 支持中文、英文、日文、韩文等多种语言
- **翻译缓存**: 避免重复翻译，提高性能

### 🔖 书签系统

- **添加书签**: 为任意段落添加带标题和备注的书签
- **书签管理**: 查看、跳转和删除书签
- **智能排序**: 按段落顺序自动排列书签

### 🎤 语音选择

- **多提供商支持**: 浏览器内置 TTS 和 Microsoft Edge TTS
- **智能推荐**: 根据文本语言自动推荐最佳语音
- **语音预览**: 实时切换不同语音和提供商
- **质量对比**: Edge TTS 音质更佳，浏览器 TTS 响应更快

### ⚙️ 个性化设置

- **主题切换**: 浅色、深色、护眼三种主题
- **字体调节**: 字体大小和行高可调
- **布局控制**: 翻译面板和书签面板的显示控制

## 🎮 快捷键

| 功能          | 快捷键       |
| ------------- | ------------ |
| 播放/暂停     | `空格`       |
| 停止朗读      | `Esc`        |
| 上一段/下一段 | `Ctrl + ←/→` |
| 加速/减速     | `Ctrl + ↑/↓` |
| 切换翻译      | `Ctrl + T`   |

## 🚀 使用步骤

### 1. 上传书籍

1. 访问 `/reader` 页面
2. 点击"上传书籍"按钮或拖拽文件到上传区域
3. 选择 TXT 格式的文本文件
4. 等待文件处理完成

### 2. 开始阅读

1. 在书籍库中点击要阅读的书籍
2. 进入阅读器界面
3. 点击播放按钮开始语音朗读
4. 使用控制栏调节语速、音量等参数

### 3. 使用高级功能

- **选择语音**: 点击语音按钮选择不同的 TTS 提供商和语音
- **添加书签**: 点击书签按钮，为当前位置添加书签
- **查看翻译**: 点击翻译按钮显示翻译面板
- **调整设置**: 点击设置按钮自定义阅读体验

## 🛠️ 技术特性

### 前端技术栈

- **React 19**: 最新的 React 版本，支持并发特性
- **TypeScript**: 类型安全的开发体验
- **Tailwind CSS**: 现代化的样式框架
- **Vite**: 快速的构建工具

### 语音技术

- **多TTS引擎**: 浏览器原生 + Microsoft Edge TTS
- **智能语言检测**: 自动识别文本语言并选择最佳语音
- **智能分段**: 按段落和句子智能分割文本
- **精确同步**: 单词级别的高亮同步
- **质量优化**: Edge TTS 提供更自然的语音效果

### 数据存储

- **LocalStorage**: 本地存储书籍和设置
- **缓存机制**: 翻译结果缓存，提高性能
- **进度保存**: 自动保存阅读进度

## 📱 响应式设计

- **桌面端**: 完整功能体验，多面板布局
- **平板端**: 适配中等屏幕，优化触控操作
- **手机端**: 简化界面，保留核心功能

## 🔧 浏览器兼容性

- **Chrome**: 完全支持（推荐）
- **Firefox**: 基本支持
- **Safari**: 基本支持
- **Edge**: 完全支持

## 📊 性能优化

- **懒加载**: 按需加载组件和资源
- **缓存策略**: 智能缓存翻译和语音数据
- **内存管理**: 自动清理不需要的资源
- **流畅动画**: 使用 CSS 变换优化动画性能

## 🎯 使用建议

### 最佳实践

1. **文件准备**: 使用格式良好的 TXT 文件，段落间用空行分隔
2. **环境设置**: 在安静的环境中使用，佩戴耳机获得最佳体验
3. **语速调节**: 根据内容难度调整合适的语速
4. **定期休息**: 长时间使用时注意眼部和听力休息

### 故障排除

- **语音不工作**: 检查浏览器权限设置，确保允许音频播放
- **翻译失败**: 检查网络连接，某些翻译服务可能有访问限制
- **文件上传失败**: 确保文件格式为 TXT 且大小不超过 10MB

## 🔮 未来规划

- **EPUB 支持**: 支持更多电子书格式
- **PDF 解析**: 智能提取 PDF 文本内容
- **云端同步**: 跨设备同步阅读进度和书签
- **AI 增强**: 智能摘要和内容推荐
- **语音克隆**: 自定义语音合成
- **协作功能**: 分享书签和笔记

## 📞 技术支持

如果您在使用过程中遇到问题，请：

1. 检查浏览器控制台是否有错误信息
2. 尝试刷新页面或清除浏览器缓存
3. 确保使用支持的浏览器版本

---

**享受您的智能阅读体验！** 📖✨
