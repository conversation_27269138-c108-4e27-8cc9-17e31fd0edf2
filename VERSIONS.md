# 技术栈版本信息

## 已更新到最新版本 ✅

### 前端技术栈
- **React**: `19.1.1` (最新稳定版，2024年12月发布)
- **React DOM**: `19.1.1`
- **React Router**: `7.1.1` (最新版)
- **Vite**: `7.1.2` (最新版)
- **TypeScript**: `5.7.2` (最新版)
- **TanStack Query**: `5.85.3` (最新版)
- **Tailwind CSS**: `4.1.12` (最新版 v4.1)
- **Tailwind Merge**: `3.3.1` (最新版)
- **@tailwindcss/vite**: `4.1.12` (Tailwind v4 Vite 插件)
- **ESLint**: `9.17.0` (最新版)

### 后端技术栈
- **FastAPI**: `0.116.1` (最新版，2024年7月发布)
- **Uvicorn**: `0.34.0` (最新版)
- **Pydantic**: `2.10.4` (最新版)
- **SQLAlchemy**: `2.0.36` (最新版)

### 数据库技术栈
- **Prisma**: `6.14.0` (最新版)
- **PostgreSQL**: `16-alpine` (最新稳定版)
- **Redis**: `7-alpine` (最新稳定版)

### 构建工具
- **Turborepo**: `2.5.6` (最新版)
- **pnpm**: `9.15.0` (最新版)
- **Prettier**: `3.4.2` (最新版)

## React 19 的重要变化

### 1. 新的 React DOM API
```tsx
// 旧版本 (React 18)
import React from 'react'
import ReactDOM from 'react-dom/client'

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
)

// 新版本 (React 19)
import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>
)
```

### 2. 新的编译器特性
- React 19 引入了新的编译器优化
- 自动优化组件重新渲染
- 更好的开发体验

### 3. 新的 Hooks
- `use()` Hook for data fetching
- 改进的 `useEffect` 行为
- 更好的并发特性

## FastAPI 0.116 的新特性

### 1. 性能改进
- 更快的启动时间
- 优化的依赖注入系统
- 改进的异步处理

### 2. 新的功能
- 更好的类型提示支持
- 改进的错误处理
- 新的中间件系统

## Vite 7 的新特性

### 1. 性能提升
- 更快的冷启动
- 优化的 HMR (热模块替换)
- 改进的构建性能

### 2. 新功能
- 更好的 TypeScript 支持
- 改进的插件系统
- 新的开发服务器功能

## Tailwind CSS v4.1 升级说明

### 🎨 **已升级到 Tailwind CSS v4.1！**

由于这是新项目，没有历史负担，我们直接使用了最新的 Tailwind CSS v4.1。

#### ✨ **Tailwind CSS v4.1 的新特性**
- **更好的性能**: 更快的构建速度和更小的包体积
- **新的 CSS 引擎**: 基于 Lightning CSS 的全新引擎
- **简化的配置**: 通过 CSS 而不是 JavaScript 配置
- **原生 CSS 特性**: 更好地利用现代 CSS 功能
- **Vite 集成**: 专门的 `@tailwindcss/vite` 插件

#### 🔧 **主要变化**

1. **配置方式**:
   ```css
   /* 新的配置方式 - 在 CSS 中 */
   @import "tailwindcss";

   @theme {
     --font-sans: Inter, system-ui, sans-serif;
     --color-primary-500: #3b82f6;
   }
   ```

2. **Vite 插件**:
   ```ts
   // vite.config.ts
   import tailwindcss from '@tailwindcss/vite'

   export default defineConfig({
     plugins: [react(), tailwindcss()],
   })
   ```

3. **类名变化**:
   - `shadow-sm` → `shadow-xs`
   - `shadow` → `shadow-sm`
   - `rounded-md` → `rounded-sm`
   - `focus:ring-primary-500` → `focus:ring-3 focus:ring-primary-500`

#### 📦 **移除的依赖**
- ❌ `postcss.config.js` (不再需要)
- ❌ `tailwind.config.js` (配置移到 CSS 中)
- ❌ `autoprefixer` (内置处理)
- ❌ `postcss` (通过 Vite 插件处理)

## 关于 Docker 的说明

Docker 在这个项目中的作用：

### 🎯 主要用途
- **本地开发便利性**: 快速启动 PostgreSQL 和 Redis
- **环境一致性**: 确保所有开发者使用相同的数据库版本
- **零配置**: 无需手动安装和配置数据库

### 🔧 使用场景
```bash
# 启动数据库服务
docker-compose up -d postgres redis

# 查看服务状态
docker-compose ps

# 停止服务
docker-compose down
```

### 🚫 不是必需的
如果你已经有本地的 PostgreSQL 和 Redis：
1. 跳过 Docker 步骤
2. 更新 `.env` 文件中的连接字符串
3. 直接运行 `pnpm dev`

### 📦 服务配置
- **PostgreSQL**: 端口 5432，数据库 `reading_platform`
- **Redis**: 端口 6379，用于缓存和任务队列
- **数据持久化**: 使用 Docker volumes 保存数据

## 升级建议

### 从旧版本升级
如果你有现有的项目需要升级：

1. **React 18 → 19**:
   ```bash
   pnpm add react@19.1.1 react-dom@19.1.1
   pnpm add -D @types/react@19.1.1 @types/react-dom@19.1.1
   ```

2. **Vite 6 → 7**:
   ```bash
   pnpm add -D vite@7.1.2
   ```

3. **FastAPI 0.115 → 0.116**:
   ```bash
   pip install fastapi>=0.116.1
   ```

### 注意事项
- React 19 可能有一些破坏性变化，请查看官方迁移指南
- Vite 7 改进了 TypeScript 支持，可能需要更新配置
- FastAPI 0.116 向后兼容，升级应该很平滑

## 开发环境要求

### 最低版本要求
- **Node.js**: >= 18.0.0
- **Python**: >= 3.11
- **pnpm**: >= 9.0.0
- **Docker**: >= 20.0.0 (可选)

### 推荐版本
- **Node.js**: 20.x LTS
- **Python**: 3.12.x
- **pnpm**: 9.15.x
- **Docker**: 最新稳定版
