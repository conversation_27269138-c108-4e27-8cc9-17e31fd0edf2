#!/bin/bash

# Development startup script

set -e

echo "🚀 Starting Reading Platform in development mode..."

# Check if .env exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please run setup.sh first."
    exit 1
fi

# Start database services
echo "🐳 Starting database services..."
docker-compose up -d postgres redis

# Wait for services
echo "⏳ Waiting for services to be ready..."
sleep 5

# Start all development servers
echo "🔧 Starting development servers..."
pnpm dev
