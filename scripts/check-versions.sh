#!/bin/bash

# Version Check Script for Reading Platform

echo "🔍 Checking installed versions..."
echo "=================================="

# Check Node.js
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    echo "✅ Node.js: $NODE_VERSION"
else
    echo "❌ Node.js: Not installed"
fi

# Check pnpm
if command -v pnpm &> /dev/null; then
    PNPM_VERSION=$(pnpm --version)
    echo "✅ pnpm: v$PNPM_VERSION"
else
    echo "❌ pnpm: Not installed"
fi

# Check Python
if command -v python3 &> /dev/null; then
    PYTHON_VERSION=$(python3 --version)
    echo "✅ Python: $PYTHON_VERSION"
else
    echo "❌ Python: Not installed"
fi

# Check Docker
if command -v docker &> /dev/null; then
    DOCKER_VERSION=$(docker --version)
    echo "✅ Docker: $DOCKER_VERSION"
else
    echo "⚠️  Docker: Not installed (optional for local development)"
fi

echo ""
echo "📦 Checking package versions..."
echo "================================"

# Check if package.json exists
if [ -f "package.json" ]; then
    echo "📋 Root package.json dependencies:"
    
    # Check Turbo version
    if command -v pnpm &> /dev/null; then
        TURBO_VERSION=$(pnpm list turbo --depth=0 2>/dev/null | grep turbo | awk '{print $2}' || echo "Not installed")
        echo "  - Turbo: $TURBO_VERSION"
        
        PRETTIER_VERSION=$(pnpm list prettier --depth=0 2>/dev/null | grep prettier | awk '{print $2}' || echo "Not installed")
        echo "  - Prettier: $PRETTIER_VERSION"
        
        TS_VERSION=$(pnpm list typescript --depth=0 2>/dev/null | grep typescript | awk '{print $2}' || echo "Not installed")
        echo "  - TypeScript: $TS_VERSION"
    fi
else
    echo "❌ package.json not found in root directory"
fi

# Check frontend dependencies
if [ -f "apps/web/package.json" ]; then
    echo ""
    echo "🌐 Frontend (apps/web) dependencies:"
    cd apps/web
    
    if command -v pnpm &> /dev/null; then
        REACT_VERSION=$(pnpm list react --depth=0 2>/dev/null | grep " react@" | awk '{print $2}' || echo "Not installed")
        echo "  - React: $REACT_VERSION"
        
        VITE_VERSION=$(pnpm list vite --depth=0 2>/dev/null | grep " vite@" | awk '{print $2}' || echo "Not installed")
        echo "  - Vite: $VITE_VERSION"
        
        QUERY_VERSION=$(pnpm list @tanstack/react-query --depth=0 2>/dev/null | grep "@tanstack/react-query" | awk '{print $2}' || echo "Not installed")
        echo "  - TanStack Query: $QUERY_VERSION"

        TAILWIND_VERSION=$(pnpm list tailwindcss --depth=0 2>/dev/null | grep " tailwindcss@" | awk '{print $2}' || echo "Not installed")
        echo "  - Tailwind CSS: $TAILWIND_VERSION"

        TAILWIND_VITE_VERSION=$(pnpm list @tailwindcss/vite --depth=0 2>/dev/null | grep "@tailwindcss/vite" | awk '{print $2}' || echo "Not installed")
        echo "  - Tailwind Vite Plugin: $TAILWIND_VITE_VERSION"

        TAILWIND_MERGE_VERSION=$(pnpm list tailwind-merge --depth=0 2>/dev/null | grep " tailwind-merge@" | awk '{print $2}' || echo "Not installed")
        echo "  - Tailwind Merge: $TAILWIND_MERGE_VERSION"
    fi
    cd ../..
fi

# Check backend dependencies
if [ -f "apps/api/pyproject.toml" ]; then
    echo ""
    echo "🐍 Backend (apps/api) dependencies:"
    cd apps/api
    
    if [ -d "venv" ]; then
        source venv/bin/activate
        
        FASTAPI_VERSION=$(pip show fastapi 2>/dev/null | grep Version | awk '{print $2}' || echo "Not installed")
        echo "  - FastAPI: $FASTAPI_VERSION"
        
        UVICORN_VERSION=$(pip show uvicorn 2>/dev/null | grep Version | awk '{print $2}' || echo "Not installed")
        echo "  - Uvicorn: $UVICORN_VERSION"
        
        PYDANTIC_VERSION=$(pip show pydantic 2>/dev/null | grep Version | awk '{print $2}' || echo "Not installed")
        echo "  - Pydantic: $PYDANTIC_VERSION"
        
        deactivate
    else
        echo "  ⚠️  Virtual environment not found. Run setup.sh first."
    fi
    cd ../..
fi

# Check database dependencies
if [ -f "packages/database/package.json" ]; then
    echo ""
    echo "🗄️  Database (packages/database) dependencies:"
    cd packages/database
    
    if command -v pnpm &> /dev/null; then
        PRISMA_VERSION=$(pnpm list @prisma/client --depth=0 2>/dev/null | grep "@prisma/client" | awk '{print $2}' || echo "Not installed")
        echo "  - Prisma Client: $PRISMA_VERSION"
        
        PRISMA_CLI_VERSION=$(pnpm list prisma --depth=0 2>/dev/null | grep " prisma@" | awk '{print $2}' || echo "Not installed")
        echo "  - Prisma CLI: $PRISMA_CLI_VERSION"
    fi
    cd ../..
fi

echo ""
echo "🎯 Expected versions (see VERSIONS.md for details):"
echo "=================================================="
echo "  - React: 19.1.1+"
echo "  - Vite: 7.1.2+"
echo "  - Tailwind CSS: 4.1.12+ (v4.1)"
echo "  - Tailwind Vite Plugin: 4.1.12+"
echo "  - Tailwind Merge: 3.3.1+"
echo "  - FastAPI: 0.116.1+"
echo "  - Prisma: 6.14.0+"
echo "  - Turbo: 2.5.6+"
echo ""
echo "✨ Version check complete!"
