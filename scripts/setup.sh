#!/bin/bash

# Reading Platform Setup Script

set -e

echo "🚀 Setting up Reading Platform..."

# Check if pnpm is installed
if ! command -v pnpm &> /dev/null; then
    echo "❌ pnpm is not installed. Please install pnpm first:"
    echo "npm install -g pnpm"
    exit 1
fi

# Check if Python 3.11+ is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.11 or higher."
    exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "⚠️  Docker is not installed. You'll need to set up PostgreSQL and Redis manually."
fi

# Install Node.js dependencies
echo "📦 Installing Node.js dependencies..."
pnpm install

# Set up environment variables
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "✅ Please edit .env file with your configuration"
else
    echo "✅ .env file already exists"
fi

# Start database services with Docker
if command -v docker &> /dev/null; then
    echo "🐳 Starting database services..."
    docker-compose up -d postgres redis
    
    # Wait for services to be ready
    echo "⏳ Waiting for services to be ready..."
    sleep 10
    
    # Generate Prisma client and run migrations
    echo "🗄️  Setting up database..."
    cd packages/database
    pnpm db:generate
    pnpm db:push
    pnpm db:seed
    cd ../..
    
    echo "✅ Database setup complete"
else
    echo "⚠️  Please set up PostgreSQL and Redis manually, then run:"
    echo "cd packages/database && pnpm db:generate && pnpm db:push && pnpm db:seed"
fi

# Set up Python environment for API
echo "🐍 Setting up Python environment..."
cd apps/api

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    python3 -m venv venv
fi

# Activate virtual environment and install dependencies
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt

cd ../..

echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Edit .env file with your API keys and configuration"
echo "2. Start the development servers:"
echo "   pnpm dev"
echo ""
echo "The application will be available at:"
echo "- Frontend: http://localhost:3000"
echo "- API: http://localhost:8000"
echo "- API Docs: http://localhost:8000/docs"
