{"name": "reading-platform", "version": "1.0.0", "description": "Translation and TTS platform monorepo", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "test": "turbo run test", "type-check": "turbo run type-check", "clean": "turbo run clean", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "db:generate": "turbo run db:generate", "db:push": "turbo run db:push", "db:migrate": "turbo run db:migrate", "db:seed": "turbo run db:seed", "db:studio": "turbo run db:studio", "setup": "./scripts/setup.sh", "check-versions": "./scripts/check-versions.sh"}, "devDependencies": {"@turbo/gen": "^2.5.6", "eslint": "^9.17.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "prettier": "^3.4.2", "turbo": "^2.5.6", "typescript": "^5.7.2", "typescript-eslint": "^8.18.1"}, "packageManager": "pnpm@9.15.0", "engines": {"node": ">=18.0.0", "pnpm": ">=9.0.0"}}