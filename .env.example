# Database
DATABASE_URL="postgresql://postgres:password@localhost:5432/reading_platform"

# Redis
REDIS_URL="redis://localhost:6379/0"

# API Keys - Translation Services
OPENAI_API_KEY=""
GOOGLE_CLOUD_PROJECT_ID=""
GOOGLE_APPLICATION_CREDENTIALS=""

# API Keys - TTS Services
AZURE_SPEECH_KEY=""
AZURE_SPEECH_REGION=""
AWS_ACCESS_KEY_ID=""
AWS_SECRET_ACCESS_KEY=""
AWS_REGION="us-east-1"

# Authentication
SECRET_KEY="your-secret-key-here"
ACCESS_TOKEN_EXPIRE_MINUTES=480
REFRESH_TOKEN_EXPIRE_MINUTES=43200

# Email (Optional)
SMTP_TLS=true
SMTP_PORT=587
SMTP_HOST=""
SMTP_USER=""
SMTP_PASSWORD=""
EMAILS_FROM_EMAIL=""
EMAILS_FROM_NAME="Reading Platform"

# File Storage
UPLOAD_DIR="uploads"
MAX_FILE_SIZE=10485760
TTS_OUTPUT_FORMAT="mp3"

# Development
LOG_LEVEL="INFO"
BACKEND_CORS_ORIGINS="http://localhost:3000,http://localhost:3001"
ALLOWED_HOSTS="localhost,127.0.0.1"
