{"name": "@reading-platform/database", "version": "1.0.0", "description": "Database schema and utilities for Reading Platform", "main": "index.js", "scripts": {"db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:deploy": "prisma migrate deploy", "db:migrate:reset": "prisma migrate reset", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist node_modules/.prisma", "type-check": "tsc --noEmit"}, "dependencies": {"@prisma/client": "^6.14.0", "prisma": "^6.14.0"}, "devDependencies": {"@reading-platform/typescript-config": "workspace:*", "@types/node": "^22.10.2", "tsx": "^4.19.2", "typescript": "^5.7.2"}, "prisma": {"schema": "prisma/schema.prisma"}, "publishConfig": {"access": "restricted"}}