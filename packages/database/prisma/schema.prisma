// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../node_modules/.prisma/client"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String?  @unique
  name      String?
  avatar    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  translations Translation[]
  ttsRequests  TTSRequest[]
  apiKeys      ApiKey[]

  @@map("users")
}

model Translation {
  id               String   @id @default(cuid())
  originalText     String
  translatedText   String
  sourceLanguage   String
  targetLanguage   String
  confidence       Float?
  provider         String   @default("google")
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relations
  userId String?
  user   User?   @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("translations")
}

model TTSRequest {
  id           String   @id @default(cuid())
  text         String
  voice        String
  speed        Float    @default(1.0)
  pitch        Float    @default(1.0)
  outputFormat String   @default("mp3")
  audioUrl     String?
  duration     Float?
  fileSize     Int?
  provider     String   @default("azure")
  status       String   @default("pending") // pending, processing, completed, failed
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  userId String?
  user   User?   @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("tts_requests")
}

model ApiKey {
  id          String   @id @default(cuid())
  name        String
  key         String   @unique
  provider    String   // google, azure, openai, aws
  isActive    Boolean  @default(true)
  lastUsedAt  DateTime?
  usageCount  Int      @default(0)
  rateLimit   Int?     // requests per minute
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("api_keys")
}

model Language {
  id          String   @id @default(cuid())
  code        String   @unique // ISO 639-1 code (e.g., 'en', 'zh')
  name        String   // Display name (e.g., 'English', 'Chinese')
  nativeName  String?  // Native name (e.g., 'English', '中文')
  isSupported Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("languages")
}

model Voice {
  id           String   @id @default(cuid())
  voiceId      String   @unique // Provider-specific voice ID
  name         String   // Display name
  language     String   // Language code
  gender       String   // male, female, neutral
  provider     String   // azure, google, aws
  sampleRate   Int      @default(22050)
  isAvailable  Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@map("voices")
}

model Usage {
  id            String   @id @default(cuid())
  userId        String?
  service       String   // translation, tts
  provider      String   // google, azure, openai, aws
  requestCount  Int      @default(0)
  characterCount Int     @default(0)
  cost          Float?   // in USD
  date          DateTime @default(now())
  createdAt     DateTime @default(now())

  @@unique([userId, service, provider, date])
  @@map("usage")
}
