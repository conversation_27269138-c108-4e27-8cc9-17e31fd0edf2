import { PrismaClient } from '../node_modules/.prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Seed languages
  const languages = [
    { code: 'en', name: 'English', nativeName: 'English' },
    { code: 'zh', name: 'Chinese (Simplified)', nativeName: '中文 (简体)' },
    { code: 'zh-TW', name: 'Chinese (Traditional)', nativeName: '中文 (繁體)' },
    { code: 'ja', name: 'Japanese', nativeName: '日本語' },
    { code: 'ko', name: 'Korean', nativeName: '한국어' },
    { code: 'es', name: 'Spanish', nativeName: 'Español' },
    { code: 'fr', name: 'French', nativeName: 'Français' },
    { code: 'de', name: 'German', nativeName: 'Deutsch' },
    { code: 'it', name: 'Italian', nativeName: 'Italiano' },
    { code: 'pt', name: 'Portuguese', nativeName: 'Português' },
    { code: 'ru', name: 'Russian', nativeName: 'Русский' },
    { code: 'ar', name: 'Arabic', nativeName: 'العربية' },
    { code: 'hi', name: 'Hindi', nativeName: 'हिन्दी' },
    { code: 'th', name: 'Thai', nativeName: 'ไทย' },
    { code: 'vi', name: 'Vietnamese', nativeName: 'Tiếng Việt' },
  ]

  for (const language of languages) {
    await prisma.language.upsert({
      where: { code: language.code },
      update: {},
      create: language,
    })
  }

  console.log('✅ Languages seeded')

  // Seed voices
  const voices = [
    {
      voiceId: 'en-US-AriaNeural',
      name: 'Aria (English US)',
      language: 'en-US',
      gender: 'female',
      provider: 'azure',
      sampleRate: 22050,
    },
    {
      voiceId: 'en-US-GuyNeural',
      name: 'Guy (English US)',
      language: 'en-US',
      gender: 'male',
      provider: 'azure',
      sampleRate: 22050,
    },
    {
      voiceId: 'zh-CN-XiaoxiaoNeural',
      name: 'Xiaoxiao (Chinese)',
      language: 'zh-CN',
      gender: 'female',
      provider: 'azure',
      sampleRate: 22050,
    },
    {
      voiceId: 'zh-CN-YunxiNeural',
      name: 'Yunxi (Chinese)',
      language: 'zh-CN',
      gender: 'male',
      provider: 'azure',
      sampleRate: 22050,
    },
    {
      voiceId: 'ja-JP-NanamiNeural',
      name: 'Nanami (Japanese)',
      language: 'ja-JP',
      gender: 'female',
      provider: 'azure',
      sampleRate: 22050,
    },
    {
      voiceId: 'es-ES-ElviraNeural',
      name: 'Elvira (Spanish)',
      language: 'es-ES',
      gender: 'female',
      provider: 'azure',
      sampleRate: 22050,
    },
  ]

  for (const voice of voices) {
    await prisma.voice.upsert({
      where: { voiceId: voice.voiceId },
      update: {},
      create: voice,
    })
  }

  console.log('✅ Voices seeded')

  // Create a demo user
  const demoUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'demo',
      name: 'Demo User',
    },
  })

  console.log('✅ Demo user created')

  // Create some sample translations
  const sampleTranslations = [
    {
      originalText: 'Hello, world!',
      translatedText: '你好，世界！',
      sourceLanguage: 'en',
      targetLanguage: 'zh',
      confidence: 0.95,
      provider: 'google',
      userId: demoUser.id,
    },
    {
      originalText: 'Good morning',
      translatedText: 'おはようございます',
      sourceLanguage: 'en',
      targetLanguage: 'ja',
      confidence: 0.98,
      provider: 'google',
      userId: demoUser.id,
    },
  ]

  for (const translation of sampleTranslations) {
    await prisma.translation.create({
      data: translation,
    })
  }

  console.log('✅ Sample translations created')

  console.log('🎉 Database seeded successfully!')
}

main()
  .catch(e => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
