{"name": "@reading-platform/eslint-config", "version": "1.0.0", "description": "Shared ESLint configuration for Reading Platform", "main": "index.js", "files": ["index.js", "react.js"], "dependencies": {"@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0"}, "devDependencies": {"eslint": "^9.17.0", "typescript": "^5.7.2"}, "peerDependencies": {"eslint": "^9.0.0", "typescript": "^5.0.0"}, "publishConfig": {"access": "restricted"}}