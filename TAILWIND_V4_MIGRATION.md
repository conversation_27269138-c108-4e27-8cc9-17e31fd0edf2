# Tailwind CSS v4.1 迁移指南

## 🎉 已完成升级到 Tailwind CSS v4.1

这个项目已经完全升级到 Tailwind CSS v4.1，以下是主要的变化和新特性。

## 📦 依赖变化

### ✅ 新增依赖
```json
{
  "devDependencies": {
    "tailwindcss": "^4.1.12",
    "@tailwindcss/vite": "^4.1.12"
  }
}
```

### ❌ 移除的依赖
- `postcss` (通过 Vite 插件处理)
- `autoprefixer` (内置处理)

### 🗑️ 删除的配置文件
- `postcss.config.js`
- `tailwind.config.js`

## 🔧 配置变化

### 1. Vite 配置更新

**之前 (v3.x)**:
```ts
// vite.config.ts
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
})
```

**现在 (v4.1)**:
```ts
// vite.config.ts
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'

export default defineConfig({
  plugins: [react(), tailwindcss()],
})
```

### 2. CSS 导入方式

**之前 (v3.x)**:
```css
/* src/index.css */
@tailwind base;
@tailwind components;
@tailwind utilities;
```

**现在 (v4.1)**:
```css
/* src/index.css */
@import "tailwindcss";
```

### 3. 主题配置

**之前 (v3.x)**:
```js
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: {
          500: '#3b82f6',
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      }
    }
  }
}
```

**现在 (v4.1)**:
```css
/* src/index.css */
@import "tailwindcss";

@theme {
  --font-sans: Inter, system-ui, sans-serif;
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
}
```

## 🎨 类名变化

### Shadow 类名
| v3.x | v4.1 |
|------|------|
| `shadow-sm` | `shadow-xs` |
| `shadow` | `shadow-sm` |
| `shadow-md` | `shadow` |
| `shadow-lg` | `shadow-md` |

### Rounded 类名
| v3.x | v4.1 |
|------|------|
| `rounded-sm` | `rounded-xs` |
| `rounded` | `rounded-sm` |
| `rounded-md` | `rounded` |
| `rounded-lg` | `rounded-md` |

### Focus Ring 类名
| v3.x | v4.1 |
|------|------|
| `focus:ring-primary-500` | `focus:ring-3 focus:ring-primary-500` |
| `focus:ring-2` | `focus:ring-3` |
| `focus:ring-1` | `focus:ring-2` |

## 🔄 已更新的组件

以下组件已经更新以适配 Tailwind CSS v4.1：

### 1. Layout.tsx
- `shadow-sm` → `shadow-xs`

### 2. HomePage.tsx
- `shadow-sm` → `shadow-xs`
- `hover:shadow-md` → `hover:shadow-sm`

### 3. TranslatePage.tsx
- `shadow-sm` → `shadow-xs`
- `rounded-md` → `rounded-sm`
- `focus:ring-primary-500` → `focus:ring-3 focus:ring-primary-500`

### 4. TTSPage.tsx
- `shadow-sm` → `shadow-xs`
- `rounded-md` → `rounded-sm`
- `focus:ring-primary-500` → `focus:ring-3 focus:ring-primary-500`

## ✨ v4.1 的新特性

### 1. 更好的性能
- **更快的构建**: 基于 Lightning CSS 的新引擎
- **更小的包体积**: 优化的 CSS 生成
- **更快的 HMR**: 改进的热模块替换

### 2. 原生 CSS 特性
- **CSS 变量**: 更好的主题支持
- **容器查询**: 原生支持 `@container`
- **CSS 嵌套**: 原生支持嵌套语法

### 3. 简化的配置
- **CSS 配置**: 通过 CSS 而不是 JavaScript
- **零配置**: 大多数情况下无需配置文件
- **类型安全**: 更好的 TypeScript 支持

## 🚀 开发体验改进

### 1. 更好的错误提示
```bash
# v4.1 提供更清晰的错误信息
Error: Unknown utility class 'shadow-sm'
Did you mean 'shadow-xs'?
```

### 2. 自动完成
- IDE 中更好的自动完成支持
- 实时的类名验证
- 更准确的类名建议

### 3. 开发工具
```bash
# 检查 Tailwind 版本
pnpm check-versions

# 查看生成的 CSS
# Vite 开发服务器会显示生成的 CSS 统计信息
```

## 🔍 验证升级

运行以下命令验证升级是否成功：

```bash
# 检查版本
pnpm check-versions

# 启动开发服务器
pnpm dev

# 构建项目
pnpm build
```

## 📚 参考资源

- [Tailwind CSS v4.0 官方文档](https://tailwindcss.com/docs)
- [v4.0 升级指南](https://tailwindcss.com/docs/upgrade-guide)
- [Lightning CSS](https://lightningcss.dev/)
- [Vite 插件文档](https://github.com/tailwindlabs/tailwindcss/tree/next/packages/%40tailwindcss-vite)

## 🎯 下一步

1. **测试所有页面**: 确保所有 UI 组件正常显示
2. **性能测试**: 验证构建速度和包体积改进
3. **添加自定义主题**: 利用新的 CSS 变量系统
4. **探索新特性**: 尝试容器查询和其他新功能

升级完成！🎉 现在你可以享受 Tailwind CSS v4.1 带来的所有新特性和性能改进。
