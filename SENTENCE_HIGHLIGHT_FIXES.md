# 句子高亮位置修复说明

## 问题分析

用户反馈的问题：
1. **高亮位置不准确**：句子换行后，高亮会从段落开头开始，重复高亮之前的句子
2. **高亮颜色不够明显**：用户无法清楚地看到当前朗读的句子
3. **重复高亮问题**：每个单词都被应用了句子高亮样式，导致整个句子都被高亮

## 根本原因

### 1. 高亮逻辑错误
**问题代码：**
```jsx
// 每个单词都被应用了句子高亮样式
<span className={`reading-text ${
  isCurrentSentence && readingState.isPlaying
    ? 'sentence-highlight-inline sentence-highlight-enter font-medium'
    : ''
}`}>
  {word}
</span>
```

**问题分析：**
- 句子高亮样式被应用到每个单词上
- 导致整个句子的所有单词都被高亮
- 当句子跨行时，视觉上看起来像是从段落开头开始高亮

### 2. 颜色透明度过低
**问题代码：**
```css
--highlight-bg: rgba(59, 130, 246, 0.06); /* 透明度太低 */
--highlight-border: rgba(59, 130, 246, 0.8);
```

## 解决方案

### 1. 修正高亮逻辑
**修复后的代码：**
```jsx
// 句子容器应用高亮样式
<span
  className={`inline relative ${
    isCurrentSentence && readingState.isPlaying
      ? 'sentence-highlight-inline sentence-highlight-enter'
      : ''
  }`}
  style={{
    color: isCurrentSentence && readingState.isPlaying 
      ? 'var(--highlight-text)' 
      : undefined
  }}
>
  {/* 单词不再应用高亮样式 */}
  {words.map((word, wordIndex) => (
    <span className="reading-text">{word}</span>
  ))}
</span>
```

**关键改进：**
- 高亮样式只应用到句子容器上
- 单词只使用基础的 `reading-text` 类
- 避免了重复高亮问题

### 2. 增强高亮颜色
**修复后的CSS变量：**
```css
:root {
  /* 亮色主题 - 明显的高亮效果 */
  --highlight-bg: rgba(59, 130, 246, 0.15); /* 提高透明度 */
  --highlight-border: rgb(59, 130, 246); /* 移除透明度 */
  --highlight-text: rgb(29, 78, 216); /* 更深的文字颜色 */
}

[data-theme='dark'] {
  /* 暗色主题 - 明显的高亮效果 */
  --highlight-bg: rgba(96, 165, 250, 0.25); /* 更高透明度 */
  --highlight-border: rgb(96, 165, 250);
  --highlight-text: rgb(191, 219, 254);
}

[data-theme='sepia'] {
  /* 护眼主题 - 明显的高亮效果 */
  --highlight-bg: rgba(59, 130, 246, 0.18);
  --highlight-border: rgb(37, 99, 235);
  --highlight-text: rgb(29, 78, 216);
}
```

### 3. 移除冗余的覆盖层
**移除的代码：**
```jsx
{/* 移除了这个冗余的背景高亮覆盖层 */}
{isCurrentSentence && readingState.isPlaying && (
  <span className="absolute inset-0 -inset-x-1 -inset-y-0.5 rounded-lg pointer-events-none bg-yellow-400/20" />
)}
```

### 4. 修复浮动高亮定位
**OptimizedReadingArea.tsx 修复：**
```jsx
// 只依赖句子元素，不再需要单词元素
const currentSentenceElement = document.querySelector(
  `[data-paragraph="${readingState.currentParagraph}"][data-sentence="${readingState.currentSentence}"]`
) as HTMLElement

// 移除了对单词元素的依赖
// const currentWordElement = document.querySelector(...) // 已删除
```

## 修复效果

### 修复前的问题
- ❌ 整个句子的每个单词都被高亮
- ❌ 句子换行时从段落开头开始高亮
- ❌ 高亮颜色太淡，不够明显
- ❌ 存在重复的高亮覆盖层

### 修复后的效果
- ✅ 只有当前朗读的句子被高亮
- ✅ 高亮准确定位到具体句子，不会错位
- ✅ 高亮颜色明显，用户可以清楚看到
- ✅ 干净的高亮实现，没有冗余代码

## 技术细节

### 1. 组件修改列表
- `ReadingArea.tsx` - 修复高亮逻辑和样式
- `HighPerformanceReadingArea.tsx` - 修复高亮逻辑和样式
- `VirtualizedReadingArea.tsx` - 修复高亮逻辑和样式
- `OptimizedReadingArea.tsx` - 修复浮动高亮定位逻辑
- `index.css` - 增强高亮颜色变量

### 2. 高亮原理
1. **句子级别高亮**：只在句子容器上应用高亮样式
2. **CSS变量驱动**：使用主题相关的CSS变量
3. **渐变背景**：使用线性渐变创建柔和的背景效果
4. **内阴影边框**：使用box-shadow模拟左边框，不占用空间

### 3. 主题适配
- **亮色主题**：蓝色系高亮，适中的对比度
- **暗色主题**：亮蓝色系高亮，在深色背景上清晰可见
- **护眼主题**：稍深的蓝色系高亮，保护眼睛

现在句子高亮功能应该能够：
- 准确定位到当前朗读的句子
- 提供明显的视觉反馈
- 在所有主题下都清晰可见
- 正确处理跨行句子的高亮
