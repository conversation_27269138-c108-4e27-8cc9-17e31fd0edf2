# Reading Platform

A modern translation and TTS (Text-to-Speech) platform built with a monorepo architecture.

## Tech Stack

- **Frontend**: Vite 7.1+ + React 19.1+ + TypeScript 5.7+
- **Backend**: FastAPI 0.116+ + Python 3.11+
- **Package Management**: pnpm 9.15+ + Turborepo 2.5+
- **Database**: PostgreSQL + Prisma 6.14+

## Project Structure

```
reading-platform/
├── apps/
│   ├── web/          # React frontend application
│   └── api/          # FastAPI backend service
├── packages/
│   ├── database/     # Prisma schema and database utilities
│   ├── typescript-config/  # Shared TypeScript configurations
│   └── eslint-config/      # Shared ESLint configurations
├── package.json
├── pnpm-workspace.yaml
└── turbo.json
```

## Getting Started

### Prerequisites

- Node.js >= 18.0.0
- pnpm >= 9.0.0
- Python >= 3.11
- PostgreSQL (or use Docker for local development)
- Redis (or use Docker for local development)

### Installation

#### Option 1: Quick Setup with Docker (Recommended for local development)

1. Run the setup script:
```bash
./scripts/setup.sh
```

This will:
- Install all dependencies
- Start PostgreSQL and Redis with Docker
- Set up the database schema
- Create environment variables

#### Option 2: Manual Setup

1. Install dependencies:
```bash
pnpm install
```

2. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your database and API configurations
```

3. Start databases (if using Docker):
```bash
docker-compose up -d postgres redis
```

4. Set up the database:
```bash
cd packages/database
pnpm db:generate
pnpm db:push
pnpm db:seed
cd ../..
```

5. Start development servers:
```bash
pnpm dev
```

**Note about Docker**: Docker is used here for convenience to run PostgreSQL and Redis locally without manual installation. If you already have these services running locally, you can skip the Docker step and update your `.env` file with the appropriate connection strings.

## Available Scripts

- `pnpm dev` - Start all development servers
- `pnpm build` - Build all applications
- `pnpm lint` - Lint all packages
- `pnpm test` - Run all tests
- `pnpm type-check` - Type check all TypeScript code
- `pnpm format` - Format code with Prettier
- `pnpm clean` - Clean all build artifacts

## Database Commands

- `pnpm db:generate` - Generate Prisma client
- `pnpm db:push` - Push schema changes to database
- `pnpm db:migrate` - Run database migrations
- `pnpm db:studio` - Open Prisma Studio

## Features

- 🌐 Multi-language translation support
- 🔊 Text-to-Speech functionality
- 📱 Responsive web interface
- 🚀 Fast development with Vite
- 🔧 Type-safe development with TypeScript
- 📦 Efficient package management with pnpm
- ⚡ Optimized builds with Turborepo
