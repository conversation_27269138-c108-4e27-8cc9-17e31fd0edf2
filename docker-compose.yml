services:
  postgres:
    image: postgres:16-alpine
    container_name: reading-platform-postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: reading_platform
    ports:
      - '5432:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U postgres']
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: reading-platform-redis
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 10s
      timeout: 5s
      retries: 5

  # Uncomment to run the API in Docker
  # api:
  #   build:
  #     context: .
  #     dockerfile: apps/api/Dockerfile
  #   container_name: reading-platform-api
  #   ports:
  #     - "8000:8000"
  #   environment:
  #     DATABASE_URL: ********************************************/reading_platform
  #     REDIS_URL: redis://redis:6379/0
  #   depends_on:
  #     postgres:
  #       condition: service_healthy
  #     redis:
  #       condition: service_healthy
  #   volumes:
  #     - ./apps/api:/app
  #     - /app/node_modules

volumes:
  postgres_data:
  redis_data:
