{"name": "@reading-platform/api", "version": "1.0.0", "description": "FastAPI backend for Reading Platform", "scripts": {"dev": "source venv/bin/activate && uvicorn app.main:app --reload --host 0.0.0.0 --port 8000", "start": "source venv/bin/activate && uvicorn app.main:app --host 0.0.0.0 --port 8000", "test": "python -m pytest", "test:watch": "python -m pytest --watch", "lint": "flake8 app tests && black --check app tests && isort --check-only app tests", "format": "black app tests && isort app tests", "type-check": "mypy app", "clean": "find . -type d -name __pycache__ -delete && find . -name '*.pyc' -delete", "db:migrate": "alembic upgrade head", "db:migration": "alembic revision --autogenerate", "db:downgrade": "alembic downgrade -1"}, "keywords": ["<PERSON><PERSON><PERSON>", "python", "api", "translation", "tts"], "private": true}