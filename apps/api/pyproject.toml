[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "reading-platform-api"
version = "1.0.0"
description = "FastAPI backend for Reading Platform"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "fastapi>=0.116.1",
    "uvicorn[standard]>=0.34.0",
    "pydantic>=2.10.4",
    "pydantic-settings>=2.7.0",
    "python-multipart>=0.0.20",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "httpx>=0.28.1",
    "aiofiles>=24.1.0",
    "python-dotenv>=1.0.1",
    "sqlalchemy>=2.0.36",
    "alembic>=1.14.0",
    "asyncpg>=0.30.0",
    "redis>=5.2.1",
    "celery>=5.4.0",
    "openai>=1.58.1",
    "google-cloud-translate>=3.18.0",
    "google-cloud-texttospeech>=2.18.0",
    "azure-cognitiveservices-speech>=1.41.1",
    "edge-tts>=7.2.0",
    "boto3>=1.35.91",
    "librosa>=0.10.2",
    "soundfile>=0.12.1",
    "numpy>=1.24.0",
    "scipy>=1.10.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=8.3.4",
    "pytest-asyncio>=0.24.0",
    "pytest-cov>=6.0.0",
    "black>=24.12.0",
    "isort>=5.13.2",
    "flake8>=7.1.1",
    "mypy>=1.14.0",
    "pre-commit>=4.0.1",
]

[tool.hatch.metadata]
allow-direct-references = true

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["app"]

[tool.mypy]
python_version = "3.11"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_return_any = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short --strict-markers"
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
]
