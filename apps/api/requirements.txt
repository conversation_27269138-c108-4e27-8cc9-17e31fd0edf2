# Core FastAPI dependencies
fastapi>=0.116.1
uvicorn[standard]>=0.34.0
pydantic>=2.10.4
pydantic-settings>=2.7.0
python-multipart>=0.0.20

# Authentication
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4

# HTTP client
httpx>=0.28.1

# File handling
aiofiles>=24.1.0

# Environment
python-dotenv>=1.0.1

# Database
sqlalchemy>=2.0.36
alembic>=1.14.0
asyncpg>=0.30.0

# Cache and task queue
redis>=5.2.1
celery>=5.4.0

# AI/ML services
openai>=1.58.1
google-cloud-translate>=3.18.0
google-cloud-texttospeech>=2.18.0
azure-cognitiveservices-speech>=1.41.1
edge-tts>=7.2.0
boto3>=1.35.91

# Audio processing
librosa>=0.10.2
soundfile>=0.12.1
numpy>=1.24.0
scipy>=1.10.0

# Development dependencies
pytest>=8.3.4
pytest-asyncio>=0.24.0
pytest-cov>=6.0.0
black>=24.12.0
isort>=5.13.2
flake8>=7.1.1
mypy>=1.14.0
pre-commit>=4.0.1
