"""
Health check endpoints.
"""

from fastapi import APIRouter
from pydantic import BaseModel

from app.core.config import settings

router = APIRouter()


class HealthResponse(BaseModel):
    """Health check response model."""
    
    status: str
    version: str
    service: str


@router.get("/", response_model=HealthResponse)
async def health_check():
    """
    Health check endpoint.
    
    Returns:
        HealthResponse: Service health status
    """
    return HealthResponse(
        status="healthy",
        version=settings.VERSION,
        service=settings.PROJECT_NAME,
    )


@router.get("/detailed")
async def detailed_health_check():
    """
    Detailed health check endpoint.
    
    Returns:
        dict: Detailed service health information
    """
    # TODO: Add database, redis, and external service health checks
    return {
        "status": "healthy",
        "version": settings.VERSION,
        "service": settings.PROJECT_NAME,
        "timestamp": "2024-01-01T00:00:00Z",
        "checks": {
            "database": "healthy",
            "redis": "healthy",
            "external_apis": "healthy",
        },
    }
