"""
Translation endpoints.
"""

from typing import Optional

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field

from app.core.config import settings
from app.services.translation import TranslationService

router = APIRouter()


class TranslationRequest(BaseModel):
    """Translation request model."""
    
    text: str = Field(..., min_length=1, max_length=settings.MAX_TRANSLATION_LENGTH)
    source_language: Optional[str] = Field(default=settings.DEFAULT_SOURCE_LANGUAGE)
    target_language: str = Field(default=settings.DEFAULT_TARGET_LANGUAGE)


class TranslationResponse(BaseModel):
    """Translation response model."""
    
    original_text: str
    translated_text: str
    source_language: str
    target_language: str
    confidence: Optional[float] = None


class LanguageDetectionRequest(BaseModel):
    """Language detection request model."""
    
    text: str = Field(..., min_length=1, max_length=1000)


class LanguageDetectionResponse(BaseModel):
    """Language detection response model."""
    
    text: str
    detected_language: str
    confidence: float


@router.post("/translate", response_model=TranslationResponse)
async def translate_text(request: TranslationRequest):
    """
    Translate text from source language to target language.
    
    Args:
        request: Translation request containing text and language preferences
        
    Returns:
        TranslationResponse: Translated text with metadata
        
    Raises:
        HTTPException: If translation fails
    """
    try:
        translation_service = TranslationService()
        result = await translation_service.translate(
            text=request.text,
            source_language=request.source_language,
            target_language=request.target_language,
        )
        
        return TranslationResponse(
            original_text=request.text,
            translated_text=result["translated_text"],
            source_language=result["source_language"],
            target_language=request.target_language,
            confidence=result.get("confidence"),
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Translation failed: {str(e)}")


@router.post("/detect-language", response_model=LanguageDetectionResponse)
async def detect_language(request: LanguageDetectionRequest):
    """
    Detect the language of the provided text.
    
    Args:
        request: Language detection request containing text
        
    Returns:
        LanguageDetectionResponse: Detected language with confidence
        
    Raises:
        HTTPException: If language detection fails
    """
    try:
        translation_service = TranslationService()
        result = await translation_service.detect_language(request.text)
        
        return LanguageDetectionResponse(
            text=request.text,
            detected_language=result["language"],
            confidence=result["confidence"],
        )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Language detection failed: {str(e)}"
        )


@router.get("/languages")
async def get_supported_languages():
    """
    Get list of supported languages for translation.
    
    Returns:
        dict: List of supported languages with codes and names
    """
    try:
        translation_service = TranslationService()
        languages = await translation_service.get_supported_languages()
        return {"languages": languages}
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get supported languages: {str(e)}"
        )
