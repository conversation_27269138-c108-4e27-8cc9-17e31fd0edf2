"""
音频同步服务 - 使用音频分析实现精确的单词级同步
"""

import io
import json
import hashlib
from typing import List, Dict, Any, Optional
import numpy as np
from scipy import signal
import librosa
import edge_tts
import asyncio
import logging

logger = logging.getLogger(__name__)

class AudioSyncService:
    """
    基于音频分析的精确同步服务
    通过分析音频波形的静音间隙来识别单词边界
    """
    
    def __init__(self):
        self.cache = {}  # 缓存已分析的结果
        
    async def generate_word_timestamps(
        self,
        text: str,
        voice: str = "en-US-AriaNeural",
        speed: float = 1.0,
    ) -> Dict[str, Any]:
        """
        生成带有精确单词时间戳的音频
        
        原理：
        1. 生成音频
        2. 分析音频波形，检测静音间隙
        3. 将静音间隙映射到单词边界
        4. 返回精确的时间戳
        """
        
        # 生成缓存键
        cache_key = hashlib.md5(f"{text}:{voice}:{speed}".encode()).hexdigest()
        
        # 检查缓存
        if cache_key in self.cache:
            logger.info(f"Using cached timestamps for key: {cache_key}")
            return self.cache[cache_key]
        
        # 生成音频
        audio_data = await self._generate_audio(text, voice, speed)
        
        # 加载音频用于分析
        audio_array, sample_rate = self._load_audio(audio_data)
        
        # 检测静音间隙
        silence_intervals = self._detect_silence(audio_array, sample_rate)
        
        # 分词
        words = text.split()
        
        # 映射单词到时间戳
        word_timestamps = self._map_words_to_timestamps(
            words, silence_intervals, len(audio_array) / sample_rate * 1000
        )
        
        result = {
            "audio_data": audio_data,
            "timestamps": word_timestamps,
            "sample_rate": sample_rate,
            "duration_ms": len(audio_array) / sample_rate * 1000
        }
        
        # 缓存结果
        self.cache[cache_key] = result
        
        return result
    
    async def _generate_audio(self, text: str, voice: str, speed: float) -> bytes:
        """生成音频数据"""
        rate = f"{int((speed - 1.0) * 100):+d}%"
        
        communicate = edge_tts.Communicate(
            text=text,
            voice=voice,
            rate=rate,
        )
        
        audio_buffer = io.BytesIO()
        async for chunk in communicate.stream():
            if chunk.get("type") == "audio":
                audio_buffer.write(chunk.get("data", b""))
        
        return audio_buffer.getvalue()
    
    def _load_audio(self, audio_data: bytes) -> tuple:
        """加载音频数据为numpy数组"""
        # 将MP3数据写入临时文件
        import tempfile
        with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as tmp:
            tmp.write(audio_data)
            tmp_path = tmp.name
        
        try:
            # 使用librosa加载音频
            audio_array, sample_rate = librosa.load(tmp_path, sr=None)
            return audio_array, sample_rate
        finally:
            import os
            os.unlink(tmp_path)
    
    def _detect_silence(
        self, 
        audio_array: np.ndarray, 
        sample_rate: int,
        silence_threshold: float = 0.01,
        min_silence_duration: float = 0.05  # 50ms
    ) -> List[tuple]:
        """
        检测音频中的静音间隙
        返回静音间隙的时间戳列表 [(start_ms, end_ms), ...]
        """
        # 计算短时能量
        frame_length = int(sample_rate * 0.025)  # 25ms帧
        hop_length = int(sample_rate * 0.010)    # 10ms步长
        
        # 计算RMS能量
        rms = librosa.feature.rms(
            y=audio_array, 
            frame_length=frame_length, 
            hop_length=hop_length
        )[0]
        
        # 标准化
        if rms.max() > 0:
            rms = rms / rms.max()
        
        # 找到静音帧
        silence_frames = rms < silence_threshold
        
        # 转换为时间戳
        silence_intervals = []
        in_silence = False
        start_frame = 0
        
        min_silence_frames = int(min_silence_duration * sample_rate / hop_length)
        
        for i, is_silent in enumerate(silence_frames):
            if is_silent and not in_silence:
                in_silence = True
                start_frame = i
            elif not is_silent and in_silence:
                in_silence = False
                duration_frames = i - start_frame
                if duration_frames >= min_silence_frames:
                    start_ms = start_frame * hop_length / sample_rate * 1000
                    end_ms = i * hop_length / sample_rate * 1000
                    silence_intervals.append((start_ms, end_ms))
        
        return silence_intervals
    
    def _map_words_to_timestamps(
        self,
        words: List[str],
        silence_intervals: List[tuple],
        total_duration_ms: float
    ) -> List[Dict[str, Any]]:
        """
        将单词映射到时间戳
        基于假设：单词之间的静音间隙对应空格
        """
        timestamps = []
        
        if not words:
            return timestamps
        
        # 如果没有检测到静音，均匀分配
        if not silence_intervals:
            duration_per_word = total_duration_ms / len(words)
            for i, word in enumerate(words):
                timestamps.append({
                    "word": word,
                    "start_ms": int(i * duration_per_word),
                    "end_ms": int((i + 1) * duration_per_word),
                    "confidence": 0.5  # 低置信度
                })
            return timestamps
        
        # 基于静音间隙分配时间戳
        current_time = 0
        silence_idx = 0
        
        for i, word in enumerate(words):
            start_ms = current_time
            
            # 找到下一个静音间隙
            if silence_idx < len(silence_intervals):
                # 单词结束于静音开始
                end_ms = silence_intervals[silence_idx][0]
                # 下一个单词从静音结束开始
                current_time = silence_intervals[silence_idx][1]
                silence_idx += 1
            else:
                # 最后的单词
                if i == len(words) - 1:
                    end_ms = total_duration_ms
                else:
                    # 均匀分配剩余时间
                    remaining_words = len(words) - i
                    remaining_time = total_duration_ms - current_time
                    duration = remaining_time / remaining_words
                    end_ms = start_ms + duration
                    current_time = end_ms
            
            timestamps.append({
                "word": word,
                "start_ms": int(start_ms),
                "end_ms": int(end_ms),
                "confidence": 0.8 if silence_idx <= len(silence_intervals) else 0.6
            })
        
        return timestamps

    async def analyze_existing_audio(
        self,
        audio_data: bytes,
        text: str
    ) -> List[Dict[str, Any]]:
        """
        分析已有音频文件，生成时间戳
        """
        audio_array, sample_rate = self._load_audio(audio_data)
        silence_intervals = self._detect_silence(audio_array, sample_rate)
        words = text.split()
        
        return self._map_words_to_timestamps(
            words, 
            silence_intervals, 
            len(audio_array) / sample_rate * 1000
        )


class EnhancedWordAlignmentService:
    """
    增强的单词对齐服务
    使用多种技术提高同步精度
    """
    
    def __init__(self):
        self.audio_sync = AudioSyncService()
    
    async def get_precise_timestamps(
        self,
        text: str,
        voice: str = "zh-CN-XiaoxiaoNeural",
        speed: float = 1.0
    ) -> Dict[str, Any]:
        """
        获取精确的单词时间戳
        结合多种方法：
        1. 音频静音检测
        2. 语音节奏模式
        3. 统计模型预测
        """
        
        # 方法1：音频分析
        result = await self.audio_sync.generate_word_timestamps(text, voice, speed)
        
        # 方法2：基于语音节奏的优化
        timestamps = self._optimize_with_speech_rhythm(
            result["timestamps"], 
            text,
            voice
        )
        
        # 方法3：平滑处理
        timestamps = self._smooth_timestamps(timestamps)
        
        return {
            "audio_data": result["audio_data"],
            "timestamps": timestamps,
            "duration_ms": result["duration_ms"],
            "method": "enhanced_audio_analysis"
        }
    
    def _optimize_with_speech_rhythm(
        self,
        timestamps: List[Dict[str, Any]],
        text: str,
        voice: str
    ) -> List[Dict[str, Any]]:
        """
        基于语音节奏优化时间戳
        不同语言有不同的节奏模式
        """
        
        # 检测语言
        is_chinese = any(ord(char) > 0x4e00 for char in text)
        
        if is_chinese:
            # 中文：每个字符时间相对均匀
            return self._optimize_chinese_rhythm(timestamps, text)
        else:
            # 英文：基于音节和重音
            return self._optimize_english_rhythm(timestamps, text)
    
    def _optimize_chinese_rhythm(
        self,
        timestamps: List[Dict[str, Any]],
        text: str
    ) -> List[Dict[str, Any]]:
        """中文节奏优化"""
        # 中文字符通常发音时长相似
        # 调整过短或过长的字符时间
        
        if not timestamps:
            return timestamps
        
        # 计算平均字符时长
        total_chars = sum(len(t["word"]) for t in timestamps)
        total_duration = timestamps[-1]["end_ms"] - timestamps[0]["start_ms"]
        avg_char_duration = total_duration / total_chars if total_chars > 0 else 100
        
        optimized = []
        for ts in timestamps:
            word_len = len(ts["word"])
            expected_duration = word_len * avg_char_duration
            actual_duration = ts["end_ms"] - ts["start_ms"]
            
            # 如果差异太大，调整
            if abs(actual_duration - expected_duration) > expected_duration * 0.5:
                adjusted_end = ts["start_ms"] + expected_duration
                optimized.append({
                    **ts,
                    "end_ms": int(adjusted_end),
                    "confidence": ts["confidence"] * 0.9  # 降低置信度
                })
            else:
                optimized.append(ts)
        
        return optimized
    
    def _optimize_english_rhythm(
        self,
        timestamps: List[Dict[str, Any]],
        text: str
    ) -> List[Dict[str, Any]]:
        """英文节奏优化"""
        # 基于音节数调整
        import re
        
        def count_syllables(word):
            """简单的音节计数"""
            word = word.lower()
            vowels = "aeiou"
            syllables = 0
            previous_was_vowel = False
            
            for char in word:
                is_vowel = char in vowels
                if is_vowel and not previous_was_vowel:
                    syllables += 1
                previous_was_vowel = is_vowel
            
            return max(1, syllables)
        
        optimized = []
        for ts in timestamps:
            syllables = count_syllables(ts["word"])
            
            # 基于音节数调整时长
            min_duration = syllables * 150  # 每音节最少150ms
            actual_duration = ts["end_ms"] - ts["start_ms"]
            
            if actual_duration < min_duration:
                optimized.append({
                    **ts,
                    "end_ms": ts["start_ms"] + min_duration,
                    "confidence": ts["confidence"] * 0.9
                })
            else:
                optimized.append(ts)
        
        return optimized
    
    def _smooth_timestamps(
        self,
        timestamps: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        平滑时间戳，确保连续性
        """
        if len(timestamps) <= 1:
            return timestamps
        
        smoothed = []
        for i, ts in enumerate(timestamps):
            if i == 0:
                smoothed.append(ts)
            else:
                # 确保当前开始时间不早于前一个结束时间
                prev_end = smoothed[-1]["end_ms"]
                if ts["start_ms"] < prev_end:
                    smoothed.append({
                        **ts,
                        "start_ms": prev_end,
                        "end_ms": max(ts["end_ms"], prev_end + 50)  # 至少50ms
                    })
                else:
                    smoothed.append(ts)
        
        return smoothed


# 单例实例
audio_sync_service = AudioSyncService()
word_alignment_service = EnhancedWordAlignmentService()
