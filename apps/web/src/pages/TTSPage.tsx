import { useEffect, useMemo, useRef, useState } from 'react'
import { Play, Pause, Square, Download, Volume2 } from 'lucide-react'
import { fetchVoices, synthesizeWithTimings, type VoiceInfo } from '../api/tts'

export default function TTSPage() {
  const [text, setText] = useState('')
  const [voices, setVoices] = useState<VoiceInfo[]>([])
  const [languageFilter, setLanguageFilter] = useState<string>('')
  const [selectedVoice, setSelectedVoice] = useState<string>('en-US-AriaNeural')
  const [isPlaying, setIsPlaying] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [audioUrl, setAudioUrl] = useState<string | null>(null)
  const [speed, setSpeed] = useState(1.0)
  const [pitch, setPitch] = useState(1.0)

  const audioRef = useRef<HTMLAudioElement>(null)

  // Fetch voices from backend (edge-tts live list)
  useEffect(() => {
    // Skip if voices already loaded
    if (voices.length > 0) return

    const controller = new AbortController()

    fetchVoices()
      .then(list => {
        if (controller.signal.aborted) return
        setVoices(list)
        // Default to a reasonable English or Chinese voice if available
        const preferred =
          list.find(v => v.id === 'zh-CN-XiaoxiaoNeural') ||
          list.find(v => v.id === 'en-US-AriaNeural') ||
          list[0]
        if (preferred) setSelectedVoice(preferred.id)
      })
      .catch(err => {
        if (!controller.signal.aborted) {
          console.error('Failed to fetch voices:', err)
        }
      })

    return () => {
      controller.abort()
    }
  }, [voices.length])

  const languages = useMemo(() => {
    const set = new Set<string>()
    voices.forEach(v => v.language && set.add(v.language))
    return Array.from(set).sort()
  }, [voices])

  // Cleanup blob URL on unmount
  useEffect(() => {
    return () => {
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl)
      }
    }
  }, [audioUrl])

  const filteredVoices = useMemo(() => {
    return voices.filter(v => !languageFilter || v.language === languageFilter)
  }, [voices, languageFilter])

  // Generate speech via backend streaming endpoint
  const generateSpeech = async () => {
    if (!text.trim()) return
    if (isLoading) return // Prevent double-click

    setIsLoading(true)
    // Stop any currently playing audio before starting a new request
    if (audioRef.current && !audioRef.current.paused) {
      audioRef.current.pause()
    }
    setIsPlaying(false)

    try {
      console.log('🎵 TTSPage: 使用 synthesize-with-timings 接口')
      const response = await synthesizeWithTimings({
        text,
        voice: selectedVoice,
        speed,
        pitch,
        output_format: 'mp3',
      })

      // Revoke the old URL before creating a new one
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl)
      }

      // 从base64创建音频URL
      const audioBlob = new Blob(
        [Uint8Array.from(atob(response.audio_base64), c => c.charCodeAt(0))],
        { type: response.mime }
      )

      const newUrl = URL.createObjectURL(audioBlob)
      setAudioUrl(newUrl)

      console.log(
        `✅ TTSPage: TTS合成成功，获得 ${response.timings?.length || 0} 个时间戳`
      )

      // Wait for next tick to ensure audio element updates its src attribute
      setTimeout(() => {
        if (audioRef.current) {
          audioRef.current.load() // Ensure the new source is loaded
          audioRef.current
            .play()
            .then(() => {
              setIsPlaying(true)
            })
            .catch(err => {
              console.error('Audio autoplay failed:', err)
              setIsPlaying(false) // Let user manually play
            })
        }
      }, 100)
    } catch (error) {
      console.error('TTS generation failed:', error)
      setIsPlaying(false)
    } finally {
      setIsLoading(false)
    }
  }

  const playAudio = () => {
    if (audioRef.current) {
      audioRef.current
        .play()
        .then(() => setIsPlaying(true))
        .catch(err => console.error('Audio play failed:', err))
    }
  }

  const pauseAudio = () => {
    if (audioRef.current && !audioRef.current.paused) {
      audioRef.current.pause()
      setIsPlaying(false)
    }
  }

  const stopAudio = () => {
    if (audioRef.current) {
      audioRef.current.pause()
      audioRef.current.currentTime = 0
      setIsPlaying(false)
    }
  }

  const downloadAudio = () => {
    if (audioUrl) {
      const a = document.createElement('a')
      a.href = audioUrl
      a.download = 'speech.mp3'
      a.click()
    }
  }

  return (
    <div className="px-4 py-8">
      <div className="max-w-5xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Text-to-Speech
        </h1>

        <div className="bg-white rounded-lg shadow-xs border p-6">
          {/* Text Input */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Text to Convert
            </label>
            <textarea
              value={text}
              onChange={e => setText(e.target.value)}
              placeholder="Enter the text you want to convert to speech..."
              className="w-full h-40 p-3 border border-gray-300 rounded-sm focus:ring-3 focus:ring-primary-500 focus:border-primary-500 resize-none"
            />
            <div className="mt-2 text-sm text-gray-500">
              {text.length} characters
            </div>
          </div>

          {/* Voice and Settings */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div className="md:col-span-2 grid grid-cols-2 gap-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Language Filter
                </label>
                <select
                  value={languageFilter}
                  onChange={e => {
                    setLanguageFilter(e.target.value)
                    // Reset selected voice to first in new filtered list
                    const firstVoice = voices.find(
                      v =>
                        e.target.value === '' || v.language === e.target.value
                    )
                    if (firstVoice) setSelectedVoice(firstVoice.id)
                  }}
                  className="w-full rounded-sm border-gray-300 shadow-xs focus:border-primary-500 focus:ring-3 focus:ring-primary-500 mb-3"
                >
                  <option value="">All Languages</option>
                  {languages.map(lang => (
                    <option key={lang} value={lang}>
                      {lang}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Voice Selection
                </label>
                <select
                  value={selectedVoice}
                  onChange={e => setSelectedVoice(e.target.value)}
                  className="w-full rounded-sm border-gray-300 shadow-xs focus:border-primary-500 focus:ring-3 focus:ring-primary-500"
                >
                  {filteredVoices.map(v => (
                    <option key={v.id} value={v.id}>
                      {v.name || v.id} ({v.gender})
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Speed: {speed}x
                </label>
                <input
                  type="range"
                  min="0.5"
                  max="2.0"
                  step="0.1"
                  value={speed}
                  onChange={e => setSpeed(parseFloat(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Pitch: {pitch}x
                </label>
                <input
                  type="range"
                  min="0.5"
                  max="2.0"
                  step="0.1"
                  value={pitch}
                  onChange={e => setPitch(parseFloat(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
              </div>
            </div>
          </div>

          {/* Controls */}
          <div className="flex items-center justify-center space-x-4 mb-6">
            <button
              onClick={generateSpeech}
              disabled={!text.trim() || !selectedVoice || isLoading}
              className="flex items-center px-4 py-2 bg-primary-600 text-white rounded-sm hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <Volume2 className="h-4 w-4 mr-2" />
              {isLoading ? 'Generating...' : 'Generate Speech'}
            </button>

            {!isPlaying ? (
              <button
                onClick={playAudio}
                disabled={!audioUrl}
                className="flex items-center px-4 py-2 bg-green-600 text-white rounded-sm hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <Play className="h-4 w-4 mr-2" />
                Play
              </button>
            ) : (
              <button
                onClick={pauseAudio}
                className="flex items-center px-4 py-2 bg-yellow-600 text-white rounded-sm hover:bg-yellow-700 transition-colors"
              >
                <Pause className="h-4 w-4 mr-2" />
                Pause
              </button>
            )}

            <button
              onClick={stopAudio}
              disabled={!isPlaying}
              className="flex items-center px-4 py-2 bg-red-600 text-white rounded-sm hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <Square className="h-4 w-4 mr-2" />
              Stop
            </button>

            <button
              onClick={downloadAudio}
              disabled={!audioUrl}
              className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-sm hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <Download className="h-4 w-4 mr-2" />
              Download
            </button>
          </div>

          {/* Audio Player - always rendered but src changes */}
          <audio
            ref={audioRef}
            src={audioUrl || undefined}
            onEnded={() => setIsPlaying(false)}
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
            className="hidden"
            preload="auto"
          />

          {/* Info */}
          <div className="bg-blue-50 border border-blue-200 rounded-sm p-4">
            <h3 className="text-sm font-medium text-blue-800 mb-2">
              Text-to-Speech Features
            </h3>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Live voices from Microsoft Edge TTS</li>
              <li>• Language filter and expanded voice list</li>
              <li>• Adjustable speed and pitch controls</li>
              <li>• Download audio files for offline use</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
