import { useState } from 'react'
import BookLibrary from '../components/reader/BookLibrary'
import BookReader from '../components/reader/BookReader'

export interface BookData {
  id: string
  title: string
  content: string
  fileName: string
  uploadedAt: Date
}

export default function ReaderPage() {
  const [currentBook, setCurrentBook] = useState<BookData | null>(null)

  const handleBookSelected = (book: BookData) => {
    setCurrentBook(book)
  }

  const handleBackToLibrary = () => {
    setCurrentBook(null)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {!currentBook ? (
        <BookLibrary onBookSelected={handleBookSelected} />
      ) : (
        <BookReader book={currentBook} onBackToLibrary={handleBackToLibrary} />
      )}
    </div>
  )
}
