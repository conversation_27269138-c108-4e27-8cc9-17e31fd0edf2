import { useState } from 'react'
import { <PERSON>R<PERSON>Lef<PERSON>, Co<PERSON>, Volume2 } from 'lucide-react'
import {
  translateText,
  COMMON_LANGUAGES,
  SUPPORTED_PROVIDERS,
} from '../api/translation'
import type { PureTranslateProviderNames } from '../services/translation'

export default function TranslatePage() {
  const [sourceText, setSourceText] = useState('')
  const [translatedText, setTranslatedText] = useState('')
  const [sourceLang, setSourceLang] = useState('auto')
  const [targetLang, setTargetLang] = useState('zh')
  const [isLoading, setIsLoading] = useState(false)
  const [provider, setProvider] = useState<PureTranslateProviderNames>('google')
  const [error, setError] = useState<string | null>(null)

  const handleTranslate = async () => {
    if (!sourceText.trim()) return

    setIsLoading(true)
    setError(null)

    try {
      const result = await translateText(
        sourceText,
        sourceLang,
        targetLang,
        provider
      )
      setTranslatedText(result.translatedText)
    } catch (error) {
      console.error('Translation failed:', error)
      setError((error as Error).message)
      setTranslatedText('')
    } finally {
      setIsLoading(false)
    }
  }

  const swapLanguages = () => {
    setSourceLang(targetLang)
    setTargetLang(sourceLang)
    setSourceText(translatedText)
    setTranslatedText(sourceText)
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  const speakText = (text: string) => {
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(text)
      speechSynthesis.speak(utterance)
    }
  }

  return (
    <div className="px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Translation Service
        </h1>

        <div className="bg-white rounded-lg shadow-xs border p-6">
          {/* Provider Selection */}
          <div className="mb-4">
            <label className="text-sm font-medium text-gray-700 mb-2 block">
              Translation Provider:
            </label>
            <select
              value={provider}
              onChange={e =>
                setProvider(e.target.value as PureTranslateProviderNames)
              }
              className="rounded-sm border-gray-300 shadow-xs focus:border-primary-500 focus:ring-3 focus:ring-primary-500"
            >
              {SUPPORTED_PROVIDERS.map(p => (
                <option key={p} value={p}>
                  {p.charAt(0).toUpperCase() + p.slice(1)}
                </option>
              ))}
            </select>
          </div>

          {/* Error Display */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-sm text-red-700 text-sm">
              {error}
            </div>
          )}

          {/* Language Selection */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <label className="text-sm font-medium text-gray-700">From:</label>
              <select
                value={sourceLang}
                onChange={e => setSourceLang(e.target.value)}
                className="rounded-sm border-gray-300 shadow-xs focus:border-primary-500 focus:ring-3 focus:ring-primary-500"
              >
                {COMMON_LANGUAGES.map(lang => (
                  <option key={lang.code} value={lang.code}>
                    {lang.name}
                  </option>
                ))}
              </select>
            </div>

            <button
              onClick={swapLanguages}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              title="Swap languages"
            >
              <ArrowRightLeft className="h-5 w-5" />
            </button>

            <div className="flex items-center space-x-4">
              <label className="text-sm font-medium text-gray-700">To:</label>
              <select
                value={targetLang}
                onChange={e => setTargetLang(e.target.value)}
                className="rounded-sm border-gray-300 shadow-xs focus:border-primary-500 focus:ring-3 focus:ring-primary-500"
              >
                {COMMON_LANGUAGES.filter(lang => lang.code !== 'auto').map(
                  lang => (
                    <option key={lang.code} value={lang.code}>
                      {lang.name}
                    </option>
                  )
                )}
              </select>
            </div>
          </div>

          {/* Text Areas */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Source Text */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="text-sm font-medium text-gray-700">
                  Source Text
                </label>
                <div className="flex space-x-2">
                  <button
                    onClick={() => speakText(sourceText)}
                    disabled={!sourceText}
                    className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                    title="Listen"
                  >
                    <Volume2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
              <textarea
                value={sourceText}
                onChange={e => setSourceText(e.target.value)}
                placeholder="Enter text to translate..."
                className="w-full h-40 p-3 border border-gray-300 rounded-sm focus:ring-3 focus:ring-primary-500 focus:border-primary-500 resize-none"
              />
            </div>

            {/* Translated Text */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="text-sm font-medium text-gray-700">
                  Translation
                </label>
                <div className="flex space-x-2">
                  <button
                    onClick={() => copyToClipboard(translatedText)}
                    disabled={!translatedText}
                    className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                    title="Copy"
                  >
                    <Copy className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => speakText(translatedText)}
                    disabled={!translatedText}
                    className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                    title="Listen"
                  >
                    <Volume2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
              <textarea
                value={translatedText}
                readOnly
                placeholder="Translation will appear here..."
                className="w-full h-40 p-3 border border-gray-300 rounded-sm bg-gray-50 resize-none"
              />
            </div>
          </div>

          {/* Translate Button */}
          <div className="mt-6 flex justify-center">
            <button
              onClick={handleTranslate}
              disabled={!sourceText.trim() || isLoading}
              className="px-6 py-2 bg-primary-600 text-white rounded-sm hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? 'Translating...' : 'Translate'}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
