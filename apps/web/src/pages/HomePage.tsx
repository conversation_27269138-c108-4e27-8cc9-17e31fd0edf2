import { Link } from 'react-router-dom'
import { Languages, Volume2, BookOpen } from 'lucide-react'

export default function HomePage() {
  return (
    <div className="px-4 py-8">
      <div className="text-center">
        <BookOpen className="mx-auto h-12 w-12 text-primary-600" />
        <h1 className="mt-4 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
          Welcome to Reading Platform
        </h1>
        <p className="mt-6 text-lg leading-8 text-gray-600">
          A modern platform for translation and text-to-speech services.
          Enhance your reading experience with powerful AI-driven tools.
        </p>
      </div>

      <div className="mt-16 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-2">
        <Link
          to="/translate"
          className="group relative rounded-2xl border border-gray-200 p-8 shadow-xs hover:shadow-sm transition-shadow"
        >
          <div>
            <span className="rounded-lg inline-flex p-3 bg-primary-50 text-primary-600 group-hover:bg-primary-100">
              <Languages className="h-6 w-6" />
            </span>
          </div>
          <div className="mt-4">
            <h3 className="text-lg font-semibold leading-6 text-gray-900">
              Translation Service
            </h3>
            <p className="mt-2 text-sm text-gray-500">
              Translate text between multiple languages with high accuracy.
              Support for real-time translation and batch processing.
            </p>
          </div>
          <span
            className="pointer-events-none absolute top-6 right-6 text-gray-300 group-hover:text-gray-400"
            aria-hidden="true"
          >
            <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
              <path d="m11.293 17.293 1.414 1.414L19.414 12l-6.707-6.707-1.414 1.414L15.586 11H5v2h10.586l-4.293 4.293z" />
            </svg>
          </span>
        </Link>

        <Link
          to="/tts"
          className="group relative rounded-2xl border border-gray-200 p-8 shadow-xs hover:shadow-sm transition-shadow"
        >
          <div>
            <span className="rounded-lg inline-flex p-3 bg-primary-50 text-primary-600 group-hover:bg-primary-100">
              <Volume2 className="h-6 w-6" />
            </span>
          </div>
          <div className="mt-4">
            <h3 className="text-lg font-semibold leading-6 text-gray-900">
              Text-to-Speech
            </h3>
            <p className="mt-2 text-sm text-gray-500">
              Convert text to natural-sounding speech with multiple voice options.
              Perfect for accessibility and content consumption.
            </p>
          </div>
          <span
            className="pointer-events-none absolute top-6 right-6 text-gray-300 group-hover:text-gray-400"
            aria-hidden="true"
          >
            <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
              <path d="m11.293 17.293 1.414 1.414L19.414 12l-6.707-6.707-1.414 1.414L15.586 11H5v2h10.586l-4.293 4.293z" />
            </svg>
          </span>
        </Link>
      </div>

      <div className="mt-16 text-center">
        <h2 className="text-2xl font-bold tracking-tight text-gray-900">
          Features
        </h2>
        <div className="mt-8 grid grid-cols-1 gap-8 sm:grid-cols-3">
          <div className="text-center">
            <div className="mx-auto h-12 w-12 rounded-md bg-primary-50 flex items-center justify-center">
              <Languages className="h-6 w-6 text-primary-600" />
            </div>
            <h3 className="mt-4 text-lg font-medium text-gray-900">Multi-language Support</h3>
            <p className="mt-2 text-sm text-gray-500">
              Support for dozens of languages with high-quality translation.
            </p>
          </div>
          <div className="text-center">
            <div className="mx-auto h-12 w-12 rounded-md bg-primary-50 flex items-center justify-center">
              <Volume2 className="h-6 w-6 text-primary-600" />
            </div>
            <h3 className="mt-4 text-lg font-medium text-gray-900">Natural Voice</h3>
            <p className="mt-2 text-sm text-gray-500">
              AI-powered text-to-speech with natural-sounding voices.
            </p>
          </div>
          <div className="text-center">
            <div className="mx-auto h-12 w-12 rounded-md bg-primary-50 flex items-center justify-center">
              <BookOpen className="h-6 w-6 text-primary-600" />
            </div>
            <h3 className="mt-4 text-lg font-medium text-gray-900">Easy to Use</h3>
            <p className="mt-2 text-sm text-gray-500">
              Simple and intuitive interface for all your reading needs.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
