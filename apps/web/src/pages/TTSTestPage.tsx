import React, { useState, useEffect, useRef } from 'react'
import { enhancedTTSService } from '../services/speech/EnhancedTTSService'

export default function TTSTestPage() {
  const [text, setText] = useState(
    'This is a precise test for perfect synchronization with advanced timing.'
  )
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentCharIndex, setCurrentCharIndex] = useState(-1)
  const [currentWordLength, setCurrentWordLength] = useState(0)
  const [debugLog, setDebugLog] = useState<string[]>([])
  const [eventStats, setEventStats] = useState({
    total: 0,
    duplicates: 0,
    lastEventTime: 0,
  })
  const textRef = useRef<HTMLDivElement>(null)

  const addDebugLog = (message: string) => {
    setDebugLog(prev => [
      ...prev.slice(-9),
      `${new Date().toLocaleTimeString()}: ${message}`,
    ])
  }

  useEffect(() => {
    const handleSpeechEvent = (event: any) => {
      switch (event.type) {
        case 'start':
          setIsPlaying(true)
          setCurrentCharIndex(-1)
          setEventStats({ total: 0, duplicates: 0, lastEventTime: 0 })
          addDebugLog('TTS started')
          break
        case 'end':
          setIsPlaying(false)
          setCurrentCharIndex(-1)
          setCurrentWordLength(0)
          addDebugLog('TTS ended')
          break
        case 'boundary':
          if (event.charIndex !== undefined) {
            const now = performance.now()
            setEventStats(prev => {
              const isDuplicate =
                event.charIndex === currentCharIndex &&
                now - prev.lastEventTime < 100
              return {
                total: prev.total + 1,
                duplicates: prev.duplicates + (isDuplicate ? 1 : 0),
                lastEventTime: now,
              }
            })

            setCurrentCharIndex(event.charIndex)
            setCurrentWordLength(event.charLength || 0)
            const word = text.substring(
              event.charIndex,
              event.charIndex + (event.charLength || 1)
            )
            addDebugLog(
              `Word boundary: "${word}" at ${event.charIndex} (${event.elapsedTime}ms)`
            )
            console.log(
              `Word boundary: charIndex=${event.charIndex}, charLength=${event.charLength}, elapsedTime=${event.elapsedTime}ms, word="${word}"`
            )
          }
          break
        case 'error':
          setIsPlaying(false)
          addDebugLog(`TTS Error: ${event.error}`)
          console.error('TTS Error:', event.error)
          break
      }
    }

    enhancedTTSService.addEventListener(handleSpeechEvent)
    return () => {
      enhancedTTSService.removeEventListener(handleSpeechEvent)
    }
  }, [])

  const handleSpeak = async () => {
    if (isPlaying) {
      enhancedTTSService.stop()
      setIsPlaying(false)
    } else {
      try {
        await enhancedTTSService.speak(text, {
          provider: 'edge-tts',
          edgeVoiceId: 'en-US-AriaNeural',
          rate: 1.0,
          pitch: 1.0,
          volume: 1.0,
        })
      } catch (error) {
        console.error('Speech failed:', error)
        setIsPlaying(false)
      }
    }
  }

  const renderTextWithHighlight = () => {
    if (currentCharIndex < 0) {
      return <span>{text}</span>
    }

    const beforeHighlight = text.substring(0, currentCharIndex)
    const highlighted = text.substring(
      currentCharIndex,
      currentCharIndex + Math.max(currentWordLength, 1)
    )
    const afterHighlight = text.substring(
      currentCharIndex + Math.max(currentWordLength, 1)
    )

    return (
      <span>
        {beforeHighlight}
        <span className="bg-yellow-300 text-black px-1 py-0.5 rounded font-semibold">
          {highlighted}
        </span>
        {afterHighlight}
      </span>
    )
  }

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          TTS 单词高亮测试
        </h1>

        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">测试文本</h2>
          <textarea
            value={text}
            onChange={e => setText(e.target.value)}
            className="w-full h-32 p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="输入要测试的文本..."
          />
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">语音播放与高亮</h2>
            <button
              onClick={handleSpeak}
              className={`px-6 py-2 rounded-lg font-medium transition-colors ${
                isPlaying
                  ? 'bg-red-500 hover:bg-red-600 text-white'
                  : 'bg-blue-500 hover:bg-blue-600 text-white'
              }`}
            >
              {isPlaying ? '停止' : '播放'}
            </button>
          </div>

          <div
            ref={textRef}
            className="text-lg leading-relaxed p-4 bg-gray-50 rounded-lg border"
            style={{ minHeight: '120px' }}
          >
            {renderTextWithHighlight()}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold mb-4">调试信息</h2>
          <div className="space-y-2 text-sm">
            <div>
              <span className="font-medium">播放状态:</span>
              <span className={isPlaying ? 'text-green-600' : 'text-red-600'}>
                {isPlaying ? '播放中' : '已停止'}
              </span>
            </div>
            <div>
              <span className="font-medium">当前字符索引:</span>
              <span className="text-blue-600">{currentCharIndex}</span>
            </div>
            <div>
              <span className="font-medium">当前单词长度:</span>
              <span className="text-blue-600">{currentWordLength}</span>
            </div>
            <div>
              <span className="font-medium">事件统计:</span>
              <span className="text-green-600">
                总计 {eventStats.total} 次，重复 {eventStats.duplicates} 次
              </span>
            </div>
            <div>
              <span className="font-medium">同步质量:</span>
              <span
                className={
                  eventStats.duplicates > eventStats.total * 0.1
                    ? 'text-red-600'
                    : 'text-green-600'
                }
              >
                {eventStats.duplicates > eventStats.total * 0.1
                  ? '需要优化'
                  : '同步良好'}
              </span>
            </div>
            {currentCharIndex >= 0 && (
              <div>
                <span className="font-medium">当前高亮单词:</span>
                <span className="text-green-600 font-mono">
                  "
                  {text.substring(
                    currentCharIndex,
                    currentCharIndex + Math.max(currentWordLength, 1)
                  )}
                  "
                </span>
              </div>
            )}
          </div>

          <div className="mt-6">
            <h3 className="text-lg font-medium mb-2">事件日志</h3>
            <div className="bg-gray-50 rounded p-3 h-32 overflow-y-auto text-xs font-mono">
              {debugLog.length === 0 ? (
                <div className="text-gray-500">等待事件...</div>
              ) : (
                debugLog.map((log, index) => (
                  <div key={index} className="mb-1">
                    {log}
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
