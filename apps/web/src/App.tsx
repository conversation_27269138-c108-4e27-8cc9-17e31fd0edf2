import { Routes, Route } from 'react-router-dom'

import Layout from './components/Layout'
import HomePage from './pages/HomePage'
import TranslatePage from './pages/TranslatePage'
import TTSPage from './pages/TTSPage'
import TTSTestPage from './pages/TTSTestPage'
import ReaderPage from './pages/ReaderPage'

function App() {
  return (
    <Layout>
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/translate" element={<TranslatePage />} />
        <Route path="/tts" element={<TTSPage />} />
        <Route path="/tts-test" element={<TTSTestPage />} />
        <Route path="/reader" element={<ReaderPage />} />
      </Routes>
    </Layout>
  )
}

export default App
