// 导出主要的翻译服务类和类型
export { TranslationService } from './TranslationService'
export type {
  TranslationOptions,
  TranslationResult,
} from './TranslationService'

// 导出类型定义
export type {
  TranslateProviderNames,
  PureTranslateProviderNames,
  ProviderConfig,
  ProvidersConfig,
} from './types/provider'

export {
  PURE_TRANSLATE_PROVIDERS,
  TRANSLATE_PROVIDER_NAMES,
  isPureTranslateProvider,
} from './types/provider'

// 导出语言相关常量和工具
export {
  ISO6393_TO_6391,
  LANG_CODE_TO_EN_NAME,
  convertToISO6391,
  getLanguageName,
} from './constants/languages'

// 导出队列相关类型
export type { QueueOptions, RequestTask } from './core/request-queue'
export { RequestQueue } from './core/request-queue'

// 导出工具函数
export { Sha256Hex } from './utils/hash'

// 导出API函数（如果需要直接使用）
export {
  googleTranslate,
  microsoftTranslate,
  deeplxTranslate,
} from './providers/api'

// 导入必要的类型和类
import { TranslationService } from './TranslationService'
import type { PureTranslateProviderNames } from './types/provider'

// 创建默认的翻译服务实例
export const defaultTranslationService = new TranslationService()

// 便捷的翻译函数
export async function translate(
  text: string,
  fromLang: string = 'auto',
  toLang: string = 'en',
  provider: PureTranslateProviderNames = 'google'
) {
  return defaultTranslationService.translate(text, {
    fromLang,
    toLang,
    provider,
  })
}
