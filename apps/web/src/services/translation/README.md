# 翻译服务 (Translation Service)

这是一个功能完整的翻译服务，支持多个翻译提供商，包含请求队列管理、缓存机制和错误处理。

## 功能特性

- **多翻译提供商支持**：Google翻译、微软翻译、DeepLX
- **请求队列管理**：使用令牌桶算法限制请求频率，避免API限制
- **缓存机制**：基于SHA256哈希的结果缓存，避免重复请求
- **错误处理**：完善的错误处理和重试机制
- **类型安全**：完整的TypeScript类型定义
- **语言代码映射**：支持多种语言代码格式转换

## 目录结构

```
src/services/translation/
├── README.md                 # 说明文档
├── index.ts                  # 主导出文件
├── TranslationService.ts     # 翻译服务主类
├── constants/
│   └── languages.ts          # 语言代码映射
├── core/
│   └── request-queue.ts      # 请求队列实现
├── providers/
│   └── api.ts               # 翻译API实现
├── types/
│   └── provider.ts          # 类型定义
├── utils/
│   └── hash.ts              # 哈希工具函数
└── test.ts                  # 测试文件
```

## 使用方法

### 基本使用

```typescript
import { translateText } from '../../api/translation'

// 翻译文本
const result = await translateText('Hello world', 'en', 'zh', 'google')
console.log(result.translatedText) // 输出: "你好世界"
```

### 高级使用

```typescript
import { TranslationService } from '../services/translation'

// 创建自定义翻译服务实例
const translationService = new TranslationService(
  {
    rate: 10, // 每秒10个请求
    capacity: 100, // 令牌桶容量
    timeoutMs: 15000, // 15秒超时
    maxRetries: 3, // 最大重试次数
    baseRetryDelayMs: 2000, // 基础重试延迟
  },
  {
    deeplx: {
      baseURL: 'https://your-deeplx-server.com',
      // Note: API keys should be handled on the backend for security
    },
  }
)

// 翻译文本
const result = await translationService.translate('Hello world', {
  fromLang: 'en',
  toLang: 'zh',
  provider: 'google',
  useCache: true,
})
```

### 批量翻译

```typescript
import { translateBatch } from '../../api/translation'

const texts = ['Hello', 'World', 'How are you?']
const results = await translateBatch(texts, 'en', 'zh', 'google')
results.forEach(result => {
  console.log(result.translatedText)
})
```

## 支持的翻译提供商

### Google 翻译

- **免费**：使用Google翻译的免费API
- **语言支持**：支持100+种语言
- **特点**：稳定可靠，无需API密钥

### Microsoft 翻译

- **免费**：使用Microsoft Edge的翻译服务
- **语言支持**：支持70+种语言
- **特点**：质量较高，但可能有访问限制

### DeepLX

- **需要配置**：需要自建或使用第三方DeepLX服务
- **语言支持**：支持30+种语言
- **特点**：翻译质量最高，适合专业翻译

## 配置选项

### 队列配置 (QueueOptions)

```typescript
interface QueueOptions {
  rate: number // 每秒请求数
  capacity: number // 令牌桶容量
  timeoutMs: number // 请求超时时间(毫秒)
  maxRetries: number // 最大重试次数
  baseRetryDelayMs: number // 基础重试延迟(毫秒)
}
```

### 提供商配置 (ProvidersConfig)

```typescript
interface ProvidersConfig {
  google?: ProviderConfig
  microsoft?: ProviderConfig
  deeplx?: ProviderConfig
  openai?: ProviderConfig // 预留
  deepseek?: ProviderConfig // 预留
  gemini?: ProviderConfig // 预留
}

interface ProviderConfig {
  apiKey?: string
  baseURL?: string
}
```

## 语言代码

服务支持多种语言代码格式：

- **ISO 639-1**: `en`, `zh`, `ja`, `ko` 等
- **ISO 639-3**: `eng`, `cmn`, `jpn`, `kor` 等
- **特殊代码**: `auto` (自动检测), `zh-TW` (繁体中文) 等

## 错误处理

服务包含完善的错误处理机制：

- **网络错误**：自动重试，指数退避
- **API错误**：详细错误信息，便于调试
- **超时处理**：可配置的请求超时时间
- **格式错误**：响应格式验证和错误提示

## 缓存机制

- **哈希键**：基于文本、提供商、源语言、目标语言生成SHA256哈希
- **内存缓存**：使用Map存储翻译结果
- **缓存控制**：可选择是否使用缓存
- **缓存清理**：提供清理缓存的方法

## 测试

在浏览器控制台中运行测试：

```javascript
// 测试翻译服务
await testTranslation()
```

## 安全注意事项

### 🚨 重要安全提醒

1. **API密钥安全**：
   - ❌ 不要在前端代码中硬编码API密钥
   - ✅ 使用后端代理模式处理API调用
   - ✅ 将API密钥存储在环境变量中

2. **数据隐私**：
   - ⚠️ 翻译内容会发送到第三方服务
   - ✅ 避免翻译包含敏感信息的文本
   - ✅ 实施数据脱敏和验证

3. **环境安全**：
   - ✅ 生产环境必须使用HTTPS
   - ✅ 实施适当的CORS策略
   - ✅ 添加用户认证和授权

### 🛡️ 内置安全功能

- **敏感数据检测**：自动检测并阻止包含敏感信息的翻译
- **文本长度限制**：防止过长文本导致的性能问题
- **环境验证**：确保在安全环境中运行
- **输入验证**：防止恶意脚本注入

### 📋 其他注意事项

1. **CORS问题**：某些翻译API可能存在CORS限制，建议在生产环境中使用代理服务器
2. **频率限制**：各个提供商都有频率限制，请合理配置请求队列参数
3. **API稳定性**：免费API可能不够稳定，生产环境建议使用付费API

## 扩展

要添加新的翻译提供商：

1. 在 `providers/api.ts` 中添加新的翻译函数
2. 在 `types/provider.ts` 中更新类型定义
3. 在 `TranslationService.ts` 中添加对应的处理逻辑
4. 更新 `api/translation.ts` 中的支持列表
