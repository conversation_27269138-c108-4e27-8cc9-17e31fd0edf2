# 翻译服务安全改进方案

## 🚨 当前安全问题

### 1. 前端密钥暴露风险 (高风险)
- API密钥可能暴露在客户端代码中
- 恶意用户可以获取并滥用API密钥
- 无法有效控制API使用量和成本

### 2. 数据隐私问题 (中风险)
- 翻译内容直接发送到第三方服务
- 敏感信息可能被第三方记录
- 缺乏数据脱敏和加密

### 3. CORS和跨域问题 (中风险)
- 直接从前端调用外部API
- 可能存在安全漏洞
- 难以实施访问控制

## 🛡️ 安全改进方案

### 方案1: 后端代理模式 (推荐)

#### 实现步骤：

1. **创建后端翻译API**
```python
# apps/api/app/api/v1/endpoints/translation.py
from fastapi import APIRouter, HTTPException, Depends
from app.core.config import settings
from app.services.translation import TranslationService

router = APIRouter()

@router.post("/translate")
async def translate_text(
    request: TranslationRequest,
    current_user: User = Depends(get_current_user)  # 需要认证
):
    try:
        # 在后端处理翻译，API密钥安全存储
        result = await TranslationService.translate(
            text=request.text,
            from_lang=request.from_lang,
            to_lang=request.to_lang,
            provider=request.provider
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

2. **前端调用后端API**
```typescript
// apps/web/src/api/translation.ts
export async function translateText(
  text: string,
  fromLang: string = 'auto',
  toLang: string = 'en',
  provider: string = 'google'
): Promise<TranslationResult> {
  const response = await fetch('/api/v1/translation/translate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${getAuthToken()}`, // 需要认证
    },
    body: JSON.stringify({
      text,
      from_lang: fromLang,
      to_lang: toLang,
      provider,
    }),
  })
  
  if (!response.ok) {
    throw new Error('Translation failed')
  }
  
  return response.json()
}
```

### 方案2: 环境变量配置

#### 当前问题修复：

1. **移除前端API密钥配置**
```typescript
// 修改 TranslationService.ts
export class TranslationService {
  constructor(
    queueOptions?: Partial<QueueOptions>,
    // 移除 providersConfig 参数，改为从环境变量读取
  ) {
    // API密钥应该从后端获取，不在前端配置
  }
}
```

2. **添加环境变量验证**
```typescript
// apps/web/src/services/translation/config.ts
interface TranslationConfig {
  enabledProviders: string[]
  defaultProvider: string
  maxTextLength: number
}

export const translationConfig: TranslationConfig = {
  enabledProviders: import.meta.env.VITE_ENABLED_TRANSLATION_PROVIDERS?.split(',') || ['google'],
  defaultProvider: import.meta.env.VITE_DEFAULT_TRANSLATION_PROVIDER || 'google',
  maxTextLength: parseInt(import.meta.env.VITE_MAX_TRANSLATION_LENGTH || '5000'),
}
```

### 方案3: 数据安全增强

#### 1. 敏感数据检测
```typescript
// apps/web/src/services/translation/security/data-filter.ts
export class DataSecurityFilter {
  private static sensitivePatterns = [
    /\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/, // 信用卡号
    /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/, // 邮箱
    /\b\d{3}-\d{2}-\d{4}\b/, // SSN
    // 添加更多敏感数据模式
  ]

  static containsSensitiveData(text: string): boolean {
    return this.sensitivePatterns.some(pattern => pattern.test(text))
  }

  static maskSensitiveData(text: string): string {
    let maskedText = text
    this.sensitivePatterns.forEach(pattern => {
      maskedText = maskedText.replace(pattern, '[REDACTED]')
    })
    return maskedText
  }
}
```

#### 2. 翻译前数据检查
```typescript
// 在翻译前检查敏感数据
export async function secureTranslate(text: string, ...args): Promise<TranslationResult> {
  if (DataSecurityFilter.containsSensitiveData(text)) {
    throw new Error('Text contains sensitive information and cannot be translated')
  }
  
  return translateText(text, ...args)
}
```

### 方案4: 访问控制和审计

#### 1. 用户认证
```typescript
// apps/web/src/services/translation/auth.ts
export async function authenticatedTranslate(
  text: string,
  options: TranslationOptions
): Promise<TranslationResult> {
  const token = getAuthToken()
  if (!token) {
    throw new Error('Authentication required for translation service')
  }
  
  // 调用需要认证的后端API
  return callBackendTranslationAPI(text, options, token)
}
```

#### 2. 使用量限制
```typescript
// apps/web/src/services/translation/rate-limiter.ts
export class UserRateLimiter {
  private static userUsage = new Map<string, number>()
  private static readonly DAILY_LIMIT = 1000 // 每日1000次翻译

  static async checkLimit(userId: string): Promise<boolean> {
    const usage = this.userUsage.get(userId) || 0
    return usage < this.DAILY_LIMIT
  }

  static incrementUsage(userId: string): void {
    const current = this.userUsage.get(userId) || 0
    this.userUsage.set(userId, current + 1)
  }
}
```

## 🔧 立即可实施的改进

### 1. 移除前端API密钥配置
```typescript
// 立即修改：移除前端API密钥选项
export interface ProviderConfig {
  // apiKey?: string  // 删除这行
  baseURL?: string
}
```

### 2. 添加数据长度限制
```typescript
// 添加文本长度验证
export async function translateText(text: string, ...args): Promise<TranslationResult> {
  if (text.length > 5000) {
    throw new Error('Text too long for translation (max 5000 characters)')
  }
  
  // 现有翻译逻辑...
}
```

### 3. 添加HTTPS检查
```typescript
// 确保只在HTTPS环境下使用
if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
  throw new Error('Translation service requires HTTPS')
}
```

## 📋 安全检查清单

- [ ] 移除前端API密钥配置
- [ ] 实施后端代理模式
- [ ] 添加用户认证
- [ ] 实施敏感数据检测
- [ ] 添加使用量限制
- [ ] 启用HTTPS
- [ ] 添加审计日志
- [ ] 实施数据脱敏
- [ ] 配置CORS策略
- [ ] 添加错误处理和监控

## 🎯 推荐实施顺序

1. **立即**: 移除前端API密钥配置
2. **短期**: 实施后端代理模式
3. **中期**: 添加数据安全检查
4. **长期**: 完善审计和监控系统
