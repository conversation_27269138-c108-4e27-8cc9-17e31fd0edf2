// 翻译提供商定义
export const PURE_TRANSLATE_PROVIDERS = [
  'google',
  'microsoft',
  'deeplx',
] as const
export const TRANSLATE_PROVIDER_NAMES = [
  'google',
  'microsoft',
  'deeplx',
  'openai',
  'deepseek',
  'gemini',
] as const

export type TranslateProviderNames = (typeof TRANSLATE_PROVIDER_NAMES)[number]
export type PureTranslateProviderNames =
  (typeof PURE_TRANSLATE_PROVIDERS)[number]

export function isPureTranslateProvider(
  provider: TranslateProviderNames
): provider is PureTranslateProviderNames {
  return PURE_TRANSLATE_PROVIDERS.includes(
    provider as PureTranslateProviderNames
  )
}

// 提供商配置
export interface ProviderConfig {
  // apiKey removed for security - should be handled on backend
  baseURL?: string
}

export type ProvidersConfig = {
  google?: ProviderConfig
  microsoft?: ProviderConfig
  deeplx?: ProviderConfig
  openai?: ProviderConfig
  deepseek?: ProviderConfig
  gemini?: ProviderConfig
}
