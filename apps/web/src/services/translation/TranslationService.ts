import { RequestQueue, QueueOptions } from './core/request-queue'
import {
  googleTranslate,
  microsoftTranslate,
  deeplxTranslate,
} from './providers/api'
import { Sha256Hex } from './utils/hash'
import {
  TranslateProviderNames,
  PureTranslateProviderNames,
  isPureTranslateProvider,
  ProvidersConfig,
} from './types/provider'
import { convertToISO6391 } from './constants/languages'

export interface TranslationOptions {
  provider?: PureTranslateProviderNames
  fromLang?: string
  toLang?: string
  useCache?: boolean
}

export interface TranslationResult {
  translatedText: string
  provider: string
  fromLang: string
  toLang: string
  cached: boolean
}

export class TranslationService {
  private requestQueue: RequestQueue
  private cache = new Map<string, string>()
  private providersConfig: ProvidersConfig

  constructor(
    queueOptions?: Partial<QueueOptions>,
    providersConfig: ProvidersConfig = {}
  ) {
    const defaultQueueOptions: QueueOptions = {
      rate: 8, // 每秒8个请求
      capacity: 200, // 令牌桶容量
      timeoutMs: 20000, // 20秒超时
      maxRetries: 2, // 最大重试次数
      baseRetryDelayMs: 1000, // 基础重试延迟
    }

    this.requestQueue = new RequestQueue({
      ...defaultQueueOptions,
      ...queueOptions,
    })

    this.providersConfig = providersConfig
  }

  /**
   * 翻译文本
   */
  async translate(
    text: string,
    options: TranslationOptions = {}
  ): Promise<TranslationResult> {
    const {
      provider = 'google',
      fromLang = 'auto',
      toLang = 'en',
      useCache = true,
    } = options

    if (!text.trim()) {
      throw new Error('Text to translate cannot be empty')
    }

    if (!isPureTranslateProvider(provider)) {
      throw new Error(`Unsupported provider: ${provider}`)
    }

    // 转换语言代码格式
    const normalizedFromLang =
      fromLang === 'auto' ? 'auto' : convertToISO6391(fromLang)
    const normalizedToLang = convertToISO6391(toLang)

    // 生成缓存键
    const cacheKey = Sha256Hex(
      text,
      provider,
      normalizedFromLang,
      normalizedToLang
    )

    // 检查缓存
    if (useCache && this.cache.has(cacheKey)) {
      return {
        translatedText: this.cache.get(cacheKey)!,
        provider,
        fromLang: normalizedFromLang,
        toLang: normalizedToLang,
        cached: true,
      }
    }

    // 创建翻译任务
    const thunk = () =>
      this.executeTranslation(
        text,
        normalizedFromLang,
        normalizedToLang,
        provider
      )

    // 加入队列执行
    const translatedText = await this.requestQueue.enqueue(
      thunk,
      Date.now(),
      cacheKey
    )

    // 缓存结果
    if (useCache) {
      this.cache.set(cacheKey, translatedText)
    }

    return {
      translatedText,
      provider,
      fromLang: normalizedFromLang,
      toLang: normalizedToLang,
      cached: false,
    }
  }

  /**
   * 执行具体的翻译请求
   */
  private async executeTranslation(
    text: string,
    fromLang: string,
    toLang: string,
    provider: PureTranslateProviderNames
  ): Promise<string> {
    switch (provider) {
      case 'google':
        return googleTranslate(text, fromLang, toLang)

      case 'microsoft':
        return microsoftTranslate(text, fromLang, toLang)

      case 'deeplx':
        const deeplxConfig = this.providersConfig.deeplx
        return deeplxTranslate(text, fromLang, toLang, {
          baseURL: deeplxConfig?.baseURL,
        })

      default:
        throw new Error(`Unsupported provider: ${provider}`)
    }
  }

  /**
   * 批量翻译
   */
  async translateBatch(
    texts: string[],
    options: TranslationOptions = {}
  ): Promise<TranslationResult[]> {
    const promises = texts.map(text => this.translate(text, options))
    return Promise.all(promises)
  }

  /**
   * 检测语言（目前只有Google支持）
   */
  async detectLanguage(text: string): Promise<string> {
    if (!text.trim()) {
      throw new Error('Text for language detection cannot be empty')
    }

    // 使用Google翻译的语言检测功能
    // 通过翻译到同一语言来检测源语言
    try {
      const result = await googleTranslate(text, 'auto', 'en')
      // 这里简化处理，实际应该解析Google API返回的语言信息
      return 'auto' // 实际实现需要解析API响应中的检测到的语言
    } catch (error) {
      throw new Error(`Language detection failed: ${(error as Error).message}`)
    }
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache.clear()
  }

  /**
   * 获取缓存大小
   */
  getCacheSize(): number {
    return this.cache.size
  }

  /**
   * 获取队列统计信息
   */
  getQueueStats() {
    return this.requestQueue.getStats()
  }

  /**
   * 清除队列
   */
  clearQueue(): void {
    this.requestQueue.clear()
  }

  /**
   * 更新提供商配置
   */
  updateProvidersConfig(config: Partial<ProvidersConfig>): void {
    this.providersConfig = {
      ...this.providersConfig,
      ...config,
    }
  }
}
