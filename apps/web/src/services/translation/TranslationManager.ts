import { translateText, translateBatch } from '../../api/translation'
import { TranslationResult } from './index'

export interface ParagraphTranslation {
  paragraphIndex: number
  originalText: string
  translatedText: string
  targetLanguage: string
  timestamp: number
  isLoading?: boolean
  error?: string
}

export interface TranslationSettings {
  enabled: boolean
  targetLanguage: string
  provider: 'google' | 'microsoft' | 'deeplx'
  autoTranslate: boolean
  visibleRangeBuffer: number // 可见区域前后缓冲的段落数量
}

export interface VisibleRange {
  start: number
  end: number
}

export type TranslationEventCallback = (
  translations: Map<number, ParagraphTranslation>
) => void

export class TranslationManager {
  private translations = new Map<number, ParagraphTranslation>()
  private translationQueue = new Set<number>()
  private isProcessingQueue = false
  private eventCallbacks: TranslationEventCallback[] = []
  private settings: TranslationSettings = {
    enabled: false,
    targetLanguage: 'zh',
    provider: 'google',
    autoTranslate: true,
    visibleRangeBuffer: 3,
  }
  private paragraphs: string[] = []
  private currentVisibleRange: VisibleRange = { start: 0, end: 0 }
  private translationCache = new Map<string, string>() // 缓存翻译结果
  private emitTimeout: NodeJS.Timeout | null = null // 防抖定时器

  constructor() {
    this.loadCachedTranslations()
  }

  // 加载缓存的翻译结果
  private loadCachedTranslations() {
    try {
      const cached = localStorage.getItem('translation-cache')
      if (cached) {
        const cacheData = JSON.parse(cached)
        this.translationCache = new Map(cacheData)
      }
    } catch (error) {
      console.warn('Failed to load translation cache:', error)
    }
  }

  // 保存翻译结果到缓存
  private saveCachedTranslations() {
    try {
      const cacheData = Array.from(this.translationCache.entries())
      localStorage.setItem('translation-cache', JSON.stringify(cacheData))
    } catch (error) {
      console.warn('Failed to save translation cache:', error)
    }
  }

  // 生成缓存键
  private getCacheKey(
    text: string,
    targetLanguage: string,
    provider: string
  ): string {
    return `${provider}:${targetLanguage}:${text.substring(0, 100)}`
  }

  // 设置段落文本
  public setParagraphs(paragraphs: string[]) {
    this.paragraphs = paragraphs
    // 清除旧的翻译结果
    this.translations.clear()
    this.translationQueue.clear()
    this.emitTranslations()
  }

  // 更新设置
  public updateSettings(newSettings: Partial<TranslationSettings>) {
    const oldSettings = { ...this.settings }
    this.settings = { ...this.settings, ...newSettings }

    // 如果语言或提供商改变，清除现有翻译
    if (
      oldSettings.targetLanguage !== this.settings.targetLanguage ||
      oldSettings.provider !== this.settings.provider
    ) {
      this.translations.clear()
      this.translationQueue.clear()
      if (this.settings.enabled && this.settings.autoTranslate) {
        this.updateVisibleRange(this.currentVisibleRange)
      }
    }

    // 如果启用翻译且自动翻译，开始翻译可见区域
    if (
      this.settings.enabled &&
      this.settings.autoTranslate &&
      !oldSettings.enabled
    ) {
      this.updateVisibleRange(this.currentVisibleRange)
    }

    this.emitTranslations()
  }

  // 更新可见区域
  public updateVisibleRange(visibleRange: VisibleRange) {
    this.currentVisibleRange = visibleRange

    if (!this.settings.enabled || !this.settings.autoTranslate) {
      return
    }

    // 计算需要翻译的范围（包括缓冲区）
    const bufferSize = this.settings.visibleRangeBuffer
    const translateStart = Math.max(0, visibleRange.start - bufferSize)
    const translateEnd = Math.min(
      this.paragraphs.length - 1,
      visibleRange.end + bufferSize
    )

    // 添加需要翻译的段落到队列
    for (let i = translateStart; i <= translateEnd; i++) {
      if (!this.translations.has(i) && !this.translationQueue.has(i)) {
        this.translationQueue.add(i)
      }
    }

    // 处理翻译队列
    this.processTranslationQueue()
  }

  // 处理翻译队列
  private async processTranslationQueue() {
    if (this.isProcessingQueue || this.translationQueue.size === 0) {
      return
    }

    this.isProcessingQueue = true

    try {
      // 批量处理翻译请求
      const batchSize = 5 // 每批处理5个段落
      const queueArray = Array.from(this.translationQueue)

      for (let i = 0; i < queueArray.length; i += batchSize) {
        const batch = queueArray.slice(i, i + batchSize)
        await this.translateBatch(batch)

        // 短暂延迟，避免API限制
        if (i + batchSize < queueArray.length) {
          await new Promise(resolve => setTimeout(resolve, 100))
        }
      }
    } catch (error) {
      console.error('Translation queue processing error:', error)
    } finally {
      this.isProcessingQueue = false
    }
  }

  // 批量翻译段落
  private async translateBatch(paragraphIndices: number[]) {
    const textsToTranslate: string[] = []
    const indexMap: number[] = []

    // 准备翻译文本，检查缓存
    for (const index of paragraphIndices) {
      if (index >= this.paragraphs.length) continue

      const text = this.paragraphs[index]
      const cacheKey = this.getCacheKey(
        text,
        this.settings.targetLanguage,
        this.settings.provider
      )

      if (this.translationCache.has(cacheKey)) {
        // 使用缓存结果
        const cachedTranslation = this.translationCache.get(cacheKey)!
        this.translations.set(index, {
          paragraphIndex: index,
          originalText: text,
          translatedText: cachedTranslation,
          targetLanguage: this.settings.targetLanguage,
          timestamp: Date.now(),
        })
        this.translationQueue.delete(index)
      } else {
        // 需要翻译
        textsToTranslate.push(text)
        indexMap.push(index)

        // 设置加载状态
        this.translations.set(index, {
          paragraphIndex: index,
          originalText: text,
          translatedText: '',
          targetLanguage: this.settings.targetLanguage,
          timestamp: Date.now(),
          isLoading: true,
        })
      }
    }

    // 发送更新事件
    this.emitTranslations()

    if (textsToTranslate.length === 0) {
      return
    }

    try {
      // 检测源语言
      const firstText = textsToTranslate[0]
      const isEnglish = /^[a-zA-Z\s.,!?;:'"()-]+$/.test(firstText.trim())
      const fromLang = isEnglish ? 'en' : 'auto'

      // 批量翻译
      const results = await translateBatch(
        textsToTranslate,
        fromLang,
        this.settings.targetLanguage,
        this.settings.provider
      )

      // 处理翻译结果
      results.forEach((result, i) => {
        const paragraphIndex = indexMap[i]
        const originalText = textsToTranslate[i]

        if (result.translatedText) {
          // 缓存翻译结果
          const cacheKey = this.getCacheKey(
            originalText,
            this.settings.targetLanguage,
            this.settings.provider
          )
          this.translationCache.set(cacheKey, result.translatedText)

          // 更新翻译结果
          this.translations.set(paragraphIndex, {
            paragraphIndex,
            originalText,
            translatedText: result.translatedText,
            targetLanguage: this.settings.targetLanguage,
            timestamp: Date.now(),
          })
        } else {
          // 翻译失败
          this.translations.set(paragraphIndex, {
            paragraphIndex,
            originalText,
            translatedText: '',
            targetLanguage: this.settings.targetLanguage,
            timestamp: Date.now(),
            error: '翻译失败',
          })
        }

        this.translationQueue.delete(paragraphIndex)
      })

      // 保存缓存
      this.saveCachedTranslations()
    } catch (error) {
      console.error('Batch translation error:', error)

      // 处理错误状态
      indexMap.forEach(paragraphIndex => {
        this.translations.set(paragraphIndex, {
          paragraphIndex,
          originalText: textsToTranslate[indexMap.indexOf(paragraphIndex)],
          translatedText: '',
          targetLanguage: this.settings.targetLanguage,
          timestamp: Date.now(),
          error: error instanceof Error ? error.message : '翻译失败',
        })
        this.translationQueue.delete(paragraphIndex)
      })
    }

    // 发送更新事件
    this.emitTranslations()
  }

  // 手动翻译单个段落
  public async translateParagraph(paragraphIndex: number): Promise<void> {
    if (paragraphIndex >= this.paragraphs.length) {
      return
    }

    const text = this.paragraphs[paragraphIndex]
    const cacheKey = this.getCacheKey(
      text,
      this.settings.targetLanguage,
      this.settings.provider
    )

    // 检查缓存
    if (this.translationCache.has(cacheKey)) {
      const cachedTranslation = this.translationCache.get(cacheKey)!
      this.translations.set(paragraphIndex, {
        paragraphIndex,
        originalText: text,
        translatedText: cachedTranslation,
        targetLanguage: this.settings.targetLanguage,
        timestamp: Date.now(),
      })
      this.emitTranslations()
      return
    }

    // 设置加载状态
    this.translations.set(paragraphIndex, {
      paragraphIndex,
      originalText: text,
      translatedText: '',
      targetLanguage: this.settings.targetLanguage,
      timestamp: Date.now(),
      isLoading: true,
    })
    this.emitTranslations()

    try {
      // 检测源语言
      const isEnglish = /^[a-zA-Z\s.,!?;:'"()-]+$/.test(text.trim())
      const fromLang = isEnglish ? 'en' : 'auto'

      const result = await translateText(
        text,
        fromLang,
        this.settings.targetLanguage,
        this.settings.provider
      )

      if (result.translatedText) {
        // 缓存翻译结果
        this.translationCache.set(cacheKey, result.translatedText)
        this.saveCachedTranslations()

        // 更新翻译结果
        this.translations.set(paragraphIndex, {
          paragraphIndex,
          originalText: text,
          translatedText: result.translatedText,
          targetLanguage: this.settings.targetLanguage,
          timestamp: Date.now(),
        })
      } else {
        // 翻译失败
        this.translations.set(paragraphIndex, {
          paragraphIndex,
          originalText: text,
          translatedText: '',
          targetLanguage: this.settings.targetLanguage,
          timestamp: Date.now(),
          error: '翻译失败',
        })
      }
    } catch (error) {
      console.error('Single paragraph translation error:', error)
      this.translations.set(paragraphIndex, {
        paragraphIndex,
        originalText: text,
        translatedText: '',
        targetLanguage: this.settings.targetLanguage,
        timestamp: Date.now(),
        error: error instanceof Error ? error.message : '翻译失败',
      })
    }

    this.emitTranslations()
  }

  // 重试翻译
  public async retryTranslation(paragraphIndex: number): Promise<void> {
    // 清除缓存中的错误结果
    const text = this.paragraphs[paragraphIndex]
    if (text) {
      const cacheKey = this.getCacheKey(
        text,
        this.settings.targetLanguage,
        this.settings.provider
      )
      this.translationCache.delete(cacheKey)
    }

    // 重新翻译
    await this.translateParagraph(paragraphIndex)
  }

  // 获取翻译结果
  public getTranslation(
    paragraphIndex: number
  ): ParagraphTranslation | undefined {
    return this.translations.get(paragraphIndex)
  }

  // 获取所有翻译结果
  public getAllTranslations(): Map<number, ParagraphTranslation> {
    return new Map(this.translations)
  }

  // 获取设置
  public getSettings(): TranslationSettings {
    return { ...this.settings }
  }

  // 清除所有翻译
  public clearTranslations(): void {
    this.translations.clear()
    this.translationQueue.clear()
    this.emitTranslations()
  }

  // 清除缓存
  public clearCache(): void {
    this.translationCache.clear()
    localStorage.removeItem('translation-cache')
  }

  // 获取缓存统计
  public getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.translationCache.size,
      keys: Array.from(this.translationCache.keys()).slice(0, 10), // 只返回前10个键作为示例
    }
  }

  // 事件监听
  public addEventListener(callback: TranslationEventCallback): void {
    this.eventCallbacks.push(callback)
  }

  public removeEventListener(callback: TranslationEventCallback): void {
    const index = this.eventCallbacks.indexOf(callback)
    if (index > -1) {
      this.eventCallbacks.splice(index, 1)
    }
  }

  // 发送翻译更新事件 - 添加防抖机制
  private emitTranslations(): void {
    if (this.emitTimeout) {
      clearTimeout(this.emitTimeout)
    }

    this.emitTimeout = setTimeout(() => {
      this.eventCallbacks.forEach(callback => {
        try {
          callback(new Map(this.translations))
        } catch (error) {
          console.error('Translation event callback error:', error)
        }
      })
      this.emitTimeout = null
    }, 50) // 50ms防抖
  }

  // 销毁实例
  public destroy(): void {
    if (this.emitTimeout) {
      clearTimeout(this.emitTimeout)
      this.emitTimeout = null
    }
    this.translations.clear()
    this.translationQueue.clear()
    this.eventCallbacks = []
    this.saveCachedTranslations()
  }
}

// 创建默认实例
export const defaultTranslationManager = new TranslationManager()
