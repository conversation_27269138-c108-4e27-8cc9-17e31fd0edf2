// ISO 639-3 到 ISO 639-1 的语言代码映射
export const ISO6393_TO_6391: Record<string, string> = {
  'eng': 'en', // English
  'cmn': 'zh', // Chinese (Mandarin)
  'cmn-Hant': 'zh-TW', // Traditional Chinese
  'spa': 'es', // Spanish
  'rus': 'ru', // Russian
  'arb': 'ar', // Arabic
  'jpn': 'ja', // Japanese
  'fra': 'fr', // French
  'deu': 'de', // German
  'kor': 'ko', // Korean
  'por': 'pt', // Portuguese
  'ita': 'it', // Italian
  'tur': 'tr', // Turkish
  'hin': 'hi', // Hindi
  'tha': 'th', // Thai
  'vie': 'vi', // Vietnamese
  'nld': 'nl', // Dutch
  'pol': 'pl', // Polish
  'swe': 'sv', // Swedish
  'dan': 'da', // Danish
  'nor': 'no', // Norwegian
  'fin': 'fi', // Finnish
  'ces': 'cs', // Czech
  'hun': 'hu', // Hungarian
  'ron': 'ro', // Romanian
  'bul': 'bg', // Bulgarian
  'hrv': 'hr', // Croatian
  'slk': 'sk', // Slovak
  'slv': 'sl', // Slovenian
  'est': 'et', // Estonian
  'lav': 'lv', // Latvian
  'lit': 'lt', // Lithuanian
  'ell': 'el', // Greek
  'heb': 'he', // Hebrew
  'urd': 'ur', // Urdu
  'ben': 'bn', // Bengali
  'tam': 'ta', // Tamil
  'tel': 'te', // Telugu
  'mar': 'mr', // Marathi
  'guj': 'gu', // Gujarati
  'kan': 'kn', // Kannada
  'mal': 'ml', // Malayalam
  'pan': 'pa', // Punjabi
  'ori': 'or', // Odia
  'asm': 'as', // Assamese
  'nep': 'ne', // Nepali
  'sin': 'si', // Sinhala
  'mya': 'my', // Myanmar
  'khm': 'km', // Khmer
  'lao': 'lo', // Lao
  'kat': 'ka', // Georgian
  'hye': 'hy', // Armenian
  'aze': 'az', // Azerbaijani
  'kaz': 'kk', // Kazakh
  'kir': 'ky', // Kyrgyz
  'uzb': 'uz', // Uzbek
  'tgk': 'tg', // Tajik
  'mon': 'mn', // Mongolian
  'bod': 'bo', // Tibetan
  'uig': 'ug', // Uyghur
  'msa': 'ms', // Malay
  'ind': 'id', // Indonesian
  'tgl': 'tl', // Filipino
  'swh': 'sw', // Swahili
  'amh': 'am', // Amharic
  'som': 'so', // Somali
  'hau': 'ha', // Hausa
  'yor': 'yo', // Yoruba
  'ibo': 'ig', // Igbo
  'zul': 'zu', // Zulu
  'xho': 'xh', // Xhosa
  'afr': 'af', // Afrikaans
}

// 语言代码到英文名称的映射
export const LANG_CODE_TO_EN_NAME: Record<string, string> = {
  'eng': 'English',
  'cmn': 'Chinese',
  'cmn-Hant': 'Traditional Chinese',
  'spa': 'Spanish',
  'rus': 'Russian',
  'arb': 'Arabic',
  'jpn': 'Japanese',
  'fra': 'French',
  'deu': 'German',
  'kor': 'Korean',
  'por': 'Portuguese',
  'ita': 'Italian',
  'tur': 'Turkish',
  'hin': 'Hindi',
  'tha': 'Thai',
  'vie': 'Vietnamese',
  'nld': 'Dutch',
  'pol': 'Polish',
  'swe': 'Swedish',
  'dan': 'Danish',
  'nor': 'Norwegian',
  'fin': 'Finnish',
  'ces': 'Czech',
  'hun': 'Hungarian',
  'ron': 'Romanian',
  'bul': 'Bulgarian',
  'hrv': 'Croatian',
  'slk': 'Slovak',
  'slv': 'Slovenian',
  'est': 'Estonian',
  'lav': 'Latvian',
  'lit': 'Lithuanian',
  'ell': 'Greek',
  'heb': 'Hebrew',
  'urd': 'Urdu',
  'ben': 'Bengali',
  'tam': 'Tamil',
  'tel': 'Telugu',
  'mar': 'Marathi',
  'guj': 'Gujarati',
  'kan': 'Kannada',
  'mal': 'Malayalam',
  'pan': 'Punjabi',
  'ori': 'Odia',
  'asm': 'Assamese',
  'nep': 'Nepali',
  'sin': 'Sinhala',
  'mya': 'Myanmar',
  'khm': 'Khmer',
  'lao': 'Lao',
  'kat': 'Georgian',
  'hye': 'Armenian',
  'aze': 'Azerbaijani',
  'kaz': 'Kazakh',
  'kir': 'Kyrgyz',
  'uzb': 'Uzbek',
  'tgk': 'Tajik',
  'mon': 'Mongolian',
  'bod': 'Tibetan',
  'uig': 'Uyghur',
  'msa': 'Malay',
  'ind': 'Indonesian',
  'tgl': 'Filipino',
  'swh': 'Swahili',
  'amh': 'Amharic',
  'som': 'Somali',
  'hau': 'Hausa',
  'yor': 'Yoruba',
  'ibo': 'Igbo',
  'zul': 'Zulu',
  'xho': 'Xhosa',
  'afr': 'Afrikaans',
}

/**
 * Convert ISO 639-3 language code to ISO 639-1
 * @param iso6393Code ISO 639-3 language code
 * @returns ISO 639-1 language code or original code if not found
 */
export function convertToISO6391(iso6393Code: string): string {
  return ISO6393_TO_6391[iso6393Code] || iso6393Code
}

/**
 * Get English name for language code
 * @param langCode Language code (ISO 639-3 or ISO 639-1)
 * @returns English name of the language
 */
export function getLanguageName(langCode: string): string {
  return LANG_CODE_TO_EN_NAME[langCode] || langCode
}
