/**
 * 基于规则的实时同步估算器
 * 不依赖后端时间戳，使用语言学规则实时估算
 */

export class RuleBasedSyncEstimator {
  private words: string[] = []
  private currentIndex = 0
  private startTime = 0
  private audioElement: HTMLAudioElement | null = null
  
  // 语言特定的发音速度（毫秒/字符）
  private readonly SPEED_FACTORS = {
    chinese: 200,  // 中文每字符约200ms
    english: 60,   // 英文每字符约60ms
    punctuation: 300, // 标点符号停顿
  }
  
  constructor() {}
  
  /**
   * 初始化估算器
   */
  initialize(text: string, audioElement: HTMLAudioElement) {
    this.audioElement = audioElement
    this.words = this.tokenize(text)
    this.currentIndex = 0
    
    // 监听音频播放
    audioElement.addEventListener('play', () => {
      this.startTime = Date.now()
      this.startTracking()
    })
    
    audioElement.addEventListener('pause', () => {
      this.stopTracking()
    })
  }
  
  /**
   * 智能分词
   */
  private tokenize(text: string): string[] {
    // 中英混合分词
    const tokens: string[] = []
    let current = ''
    let lastType = ''
    
    for (const char of text) {
      const type = this.getCharType(char)
      
      if (type !== lastType && current) {
        if (lastType === 'english') {
          // 英文按空格分词
          current.split(/\s+/).forEach(word => {
            if (word) tokens.push(word)
          })
        } else {
          // 中文逐字分词
          for (const c of current) {
            if (c.trim()) tokens.push(c)
          }
        }
        current = ''
      }
      
      current += char
      lastType = type
    }
    
    // 处理最后的部分
    if (current) {
      if (lastType === 'english') {
        current.split(/\s+/).forEach(word => {
          if (word) tokens.push(word)
        })
      } else {
        for (const c of current) {
          if (c.trim()) tokens.push(c)
        }
      }
    }
    
    return tokens
  }
  
  /**
   * 判断字符类型
   */
  private getCharType(char: string): string {
    const code = char.charCodeAt(0)
    
    // 中文
    if (code >= 0x4e00 && code <= 0x9fff) {
      return 'chinese'
    }
    
    // 英文
    if ((code >= 0x41 && code <= 0x5a) || (code >= 0x61 && code <= 0x7a)) {
      return 'english'
    }
    
    // 标点
    if (/[.,!?;:，。！？；：]/.test(char)) {
      return 'punctuation'
    }
    
    return 'other'
  }
  
  /**
   * 估算单词持续时间
   */
  private estimateDuration(word: string): number {
    let duration = 0
    
    for (const char of word) {
      const type = this.getCharType(char)
      
      switch (type) {
        case 'chinese':
          duration += this.SPEED_FACTORS.chinese
          break
        case 'english':
          duration += this.SPEED_FACTORS.english
          break
        case 'punctuation':
          duration += this.SPEED_FACTORS.punctuation
          break
        default:
          duration += 50
      }
    }
    
    // 根据音频播放速度调整
    if (this.audioElement) {
      duration = duration / this.audioElement.playbackRate
    }
    
    return duration
  }
  
  /**
   * 开始跟踪
   */
  private startTracking() {
    const track = () => {
      if (!this.audioElement || this.audioElement.paused) return
      
      const elapsed = Date.now() - this.startTime
      let accumulatedTime = 0
      let targetIndex = 0
      
      // 找到当前应该高亮的单词
      for (let i = 0; i < this.words.length; i++) {
        const duration = this.estimateDuration(this.words[i])
        
        if (accumulatedTime + duration > elapsed) {
          targetIndex = i
          break
        }
        
        accumulatedTime += duration
      }
      
      // 触发高亮事件
      if (targetIndex !== this.currentIndex) {
        this.currentIndex = targetIndex
        this.onWordHighlight(targetIndex, this.words[targetIndex])
      }
      
      // 继续跟踪
      requestAnimationFrame(track)
    }
    
    track()
  }
  
  /**
   * 停止跟踪
   */
  private stopTracking() {
    // 跟踪会自动停止
  }
  
  /**
   * 单词高亮回调
   */
  private onWordHighlight(index: number, word: string) {
    // 触发自定义事件
    window.dispatchEvent(new CustomEvent('word-highlight', {
      detail: { index, word }
    }))
  }
  
  /**
   * 根据实际播放调整速度因子
   */
  public calibrate(actualDuration: number, expectedDuration: number) {
    const ratio = actualDuration / expectedDuration
    
    this.SPEED_FACTORS.chinese *= ratio
    this.SPEED_FACTORS.english *= ratio
    this.SPEED_FACTORS.punctuation *= ratio
  }
  
  /**
   * 获取当前进度
   */
  public getProgress(): { current: number, total: number } {
    return {
      current: this.currentIndex,
      total: this.words.length
    }
  }
}

/**
 * 改进的同步策略：结合多种方法
 */
export class HybridSyncStrategy {
  private estimator: RuleBasedSyncEstimator
  private serverTimestamps: Array<{word: string, start: number, end: number}> = []
  private confidenceThreshold = 0.7
  
  constructor() {
    this.estimator = new RuleBasedSyncEstimator()
  }
  
  /**
   * 初始化混合策略
   */
  async initialize(text: string, audioUrl: string) {
    // 1. 尝试从服务器获取时间戳
    try {
      this.serverTimestamps = await this.fetchTimestamps(text, audioUrl)
    } catch (error) {
      console.warn('Failed to fetch timestamps, using estimation', error)
    }
    
    // 2. 创建音频元素
    const audio = new Audio(audioUrl)
    
    // 3. 初始化估算器作为后备
    this.estimator.initialize(text, audio)
    
    // 4. 结合两种方法
    this.setupHybridTracking(audio)
    
    return audio
  }
  
  /**
   * 从服务器获取时间戳
   */
  private async fetchTimestamps(text: string, audioUrl: string) {
    // 这里调用后端API
    // 如果后端没有准确的时间戳，返回空数组
    return []
  }
  
  /**
   * 设置混合跟踪
   */
  private setupHybridTracking(audio: HTMLAudioElement) {
    let lastServerIndex = -1
    
    audio.addEventListener('timeupdate', () => {
      const currentTime = audio.currentTime * 1000 // 转换为毫秒
      
      // 优先使用服务器时间戳
      if (this.serverTimestamps.length > 0) {
        for (let i = 0; i < this.serverTimestamps.length; i++) {
          const ts = this.serverTimestamps[i]
          if (currentTime >= ts.start && currentTime < ts.end) {
            if (i !== lastServerIndex) {
              lastServerIndex = i
              this.emitHighlight(i, ts.word, 'server')
            }
            return
          }
        }
      }
      
      // 降级到估算方法
      // 估算器会自动处理
    })
  }
  
  /**
   * 发送高亮事件
   */
  private emitHighlight(index: number, word: string, source: string) {
    window.dispatchEvent(new CustomEvent('sync-highlight', {
      detail: { index, word, source }
    }))
  }
}

// 导出实用函数
export function createSmartSync(text: string, audioUrl: string) {
  const strategy = new HybridSyncStrategy()
  return strategy.initialize(text, audioUrl)
}
