export interface SpeechOptions {
  rate: number // 语速 0.1-10
  pitch: number // 音调 0-2
  volume: number // 音量 0-1
  voice?: SpeechSynthesisVoice
  lang?: string
  provider?: 'browser' | 'edge-tts' // TTS 提供商
  edgeVoiceId?: string // Edge TTS 语音 ID
}

export interface SpeechEvent {
  type: 'start' | 'end' | 'pause' | 'resume' | 'boundary' | 'error'
  charIndex?: number
  charLength?: number
  elapsedTime?: number
  error?: string
  isPredictive?: boolean // 添加预测性同步标志
}

export type SpeechEventCallback = (event: SpeechEvent) => void

export class SpeechService {
  private synthesis: SpeechSynthesis
  private currentUtterance: SpeechSynthesisUtterance | null = null
  private isInitialized = false
  private eventCallbacks: SpeechEventCallback[] = []
  private currentText = ''
  private currentOptions: SpeechOptions = {
    rate: 1.0,
    pitch: 1.0,
    volume: 1.0,
    lang: 'en-US',
  }

  constructor() {
    this.synthesis = window.speechSynthesis
    this.initialize()
  }

  private async initialize() {
    // 等待语音合成API准备就绪
    if (this.synthesis.getVoices().length === 0) {
      await new Promise<void>(resolve => {
        const checkVoices = () => {
          if (this.synthesis.getVoices().length > 0) {
            resolve()
          } else {
            setTimeout(checkVoices, 100)
          }
        }
        this.synthesis.onvoiceschanged = checkVoices
        checkVoices()
      })
    }
    this.isInitialized = true
  }

  public async getVoices(): Promise<SpeechSynthesisVoice[]> {
    if (!this.isInitialized) {
      await this.initialize()
    }
    return this.synthesis.getVoices()
  }

  public async getVoicesByLanguage(
    lang: string
  ): Promise<SpeechSynthesisVoice[]> {
    const voices = await this.getVoices()
    return voices.filter(voice => voice.lang.startsWith(lang))
  }

  public addEventListener(callback: SpeechEventCallback) {
    this.eventCallbacks.push(callback)
  }

  public removeEventListener(callback: SpeechEventCallback) {
    const index = this.eventCallbacks.indexOf(callback)
    if (index > -1) {
      this.eventCallbacks.splice(index, 1)
    }
  }

  private emitEvent(event: SpeechEvent) {
    this.eventCallbacks.forEach(callback => callback(event))
  }

  public async speak(
    text: string,
    options: Partial<SpeechOptions> = {}
  ): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    // 停止当前播放
    this.stop()

    this.currentText = text
    this.currentOptions = { ...this.currentOptions, ...options }

    return new Promise((resolve, reject) => {
      const utterance = new SpeechSynthesisUtterance(text)

      // 设置语音参数
      utterance.rate = this.currentOptions.rate
      utterance.pitch = this.currentOptions.pitch
      utterance.volume = this.currentOptions.volume

      if (this.currentOptions.voice) {
        utterance.voice = this.currentOptions.voice
      } else if (this.currentOptions.lang) {
        utterance.lang = this.currentOptions.lang
      }

      // 设置事件监听器
      utterance.onstart = () => {
        this.emitEvent({ type: 'start' })
      }

      utterance.onend = () => {
        this.currentUtterance = null
        this.emitEvent({ type: 'end' })
        resolve()
      }

      utterance.onerror = event => {
        this.currentUtterance = null
        const error = `Speech synthesis error: ${event.error}`
        this.emitEvent({ type: 'error', error })
        reject(new Error(error))
      }

      utterance.onboundary = event => {
        this.emitEvent({
          type: 'boundary',
          charIndex: event.charIndex,
          charLength: event.charLength,
          elapsedTime: event.elapsedTime,
        })
      }

      this.currentUtterance = utterance
      this.synthesis.speak(utterance)
    })
  }

  public pause() {
    if (this.synthesis.speaking && !this.synthesis.paused) {
      this.synthesis.pause()
      this.emitEvent({ type: 'pause' })
    }
  }

  public resume() {
    if (this.synthesis.paused) {
      this.synthesis.resume()
      this.emitEvent({ type: 'resume' })
    }
  }

  public stop() {
    if (this.synthesis.speaking || this.synthesis.pending) {
      this.synthesis.cancel()
      this.currentUtterance = null
    }
  }

  public isPlaying(): boolean {
    return this.synthesis.speaking && !this.synthesis.paused
  }

  public isPaused(): boolean {
    return this.synthesis.paused
  }

  public isSpeaking(): boolean {
    return this.synthesis.speaking
  }

  public updateOptions(options: Partial<SpeechOptions>) {
    this.currentOptions = { ...this.currentOptions, ...options }

    // 如果正在播放，需要重新开始以应用新设置
    if (this.isPlaying() && this.currentText) {
      const wasPlaying = true
      this.stop()
      if (wasPlaying) {
        this.speak(this.currentText, this.currentOptions)
      }
    }
  }

  public getCurrentOptions(): SpeechOptions {
    return { ...this.currentOptions }
  }

  /**
   * @deprecated Word-level highlighting has been removed. Use speak() method instead.
   * This method now only provides sentence-level highlighting through boundary events.
   */
  public async speakWithHighlight(
    text: string,
    options: Partial<SpeechOptions> = {}
  ): Promise<void> {
    // 单词级别高亮已移除，直接调用speak方法
    return this.speak(text, options)
  }

  public destroy() {
    this.stop()
    this.eventCallbacks = []
  }
}

// 单例实例
export const speechService = new SpeechService()
