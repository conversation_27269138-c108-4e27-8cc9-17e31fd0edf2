/**
 * TTS同步测试和调试工具
 * 用于测试和优化TTS朗读与高亮的同步效果
 */

import { enhancedTTSService } from '../services/speech/EnhancedTTSService'

export interface SyncTestResult {
  avgError: number
  maxError: number
  minError: number
  errorStdDev: number
  totalWords: number
  syncAccuracy: number // 0-100的百分比
}

export class TTSSyncTester {
  private testResults: number[] = []
  private isTestingActive = false
  private testStartTime = 0
  private expectedTimings: Array<{ time_ms: number; word: string }> = []
  private actualTimings: Array<{ time_ms: number; word: string }> = []

  /**
   * 开始同步测试
   * @param text 测试文本
   * @param expectedTimings 预期的单词时间戳
   */
  public async startSyncTest(
    text: string,
    expectedTimings?: Array<{ time_ms: number; word: string }>
  ): Promise<SyncTestResult> {
    this.isTestingActive = true
    this.testResults = []
    this.actualTimings = []
    this.expectedTimings = expectedTimings || []
    this.testStartTime = performance.now()

    return new Promise((resolve) => {
      // 监听边界事件
      const handleBoundary = (event: any) => {
        if (!this.isTestingActive) return

        const actualTime = performance.now() - this.testStartTime
        
        if (this.expectedTimings.length > 0) {
          // 如果有预期时间戳，计算误差
          const expectedTime = this.expectedTimings[this.actualTimings.length]?.time_ms || 0
          const error = actualTime - expectedTime
          this.testResults.push(error)
        }

        this.actualTimings.push({
          time_ms: actualTime,
          word: event.charIndex ? text.substring(event.charIndex, event.charIndex + (event.charLength || 1)) : ''
        })
      }

      const handleEnd = () => {
        this.isTestingActive = false
        enhancedTTSService.removeEventListener(handleBoundary)
        enhancedTTSService.removeEventListener(handleEnd)
        
        const result = this.calculateTestResult()
        resolve(result)
      }

      enhancedTTSService.addEventListener(handleBoundary)
      enhancedTTSService.addEventListener((event) => {
        if (event.type === 'end') handleEnd()
      })

      // 开始朗读
      enhancedTTSService.speak(text)
    })
  }

  /**
   * 自动校准同步偏移
   * 通过多次测试找到最佳的同步参数
   */
  public async autoCalibrate(testText: string = "Hello world, this is a synchronization test."): Promise<{
    optimalOffset: number
    testResults: SyncTestResult[]
  }> {
    const testOffsets = [-100, -80, -60, -40, -20, 0, 20, 40, 60, 80, 100]
    const results: SyncTestResult[] = []
    let bestOffset = 0
    let bestAccuracy = 0

    for (const offset of testOffsets) {
      // 配置偏移量
      enhancedTTSService.configurePerfectSync({ baseOffset: offset })
      
      // 等待一段时间确保配置生效
      await new Promise(resolve => setTimeout(resolve, 100))
      
      // 运行测试
      const result = await this.startSyncTest(testText)
      results.push(result)
      
      if (result.syncAccuracy > bestAccuracy) {
        bestAccuracy = result.syncAccuracy
        bestOffset = offset
      }
      
      // 测试间隔
      await new Promise(resolve => setTimeout(resolve, 500))
    }

    // 应用最佳偏移量
    enhancedTTSService.configurePerfectSync({ baseOffset: bestOffset })

    return {
      optimalOffset: bestOffset,
      testResults: results
    }
  }

  /**
   * 实时同步监控
   * 在朗读过程中实时显示同步状态
   */
  public startRealTimeMonitoring(callback: (status: {
    currentError: number
    avgError: number
    syncQuality: 'excellent' | 'good' | 'fair' | 'poor'
  }) => void) {
    let errorHistory: number[] = []
    
    const handleBoundary = (event: any) => {
      const syncStatus = enhancedTTSService.getSyncStatus()
      const currentError = Math.abs(syncStatus.avgError)
      
      errorHistory.push(currentError)
      if (errorHistory.length > 10) errorHistory.shift()
      
      const avgError = errorHistory.reduce((a, b) => a + b, 0) / errorHistory.length
      
      let syncQuality: 'excellent' | 'good' | 'fair' | 'poor'
      if (avgError < 20) syncQuality = 'excellent'
      else if (avgError < 50) syncQuality = 'good'
      else if (avgError < 100) syncQuality = 'fair'
      else syncQuality = 'poor'
      
      callback({
        currentError,
        avgError,
        syncQuality
      })
    }

    enhancedTTSService.addEventListener(handleBoundary)
    
    return () => {
      enhancedTTSService.removeEventListener(handleBoundary)
    }
  }

  private calculateTestResult(): SyncTestResult {
    if (this.testResults.length === 0) {
      return {
        avgError: 0,
        maxError: 0,
        minError: 0,
        errorStdDev: 0,
        totalWords: 0,
        syncAccuracy: 100
      }
    }

    const avgError = this.testResults.reduce((a, b) => a + b, 0) / this.testResults.length
    const maxError = Math.max(...this.testResults)
    const minError = Math.min(...this.testResults)
    
    // 计算标准差
    const variance = this.testResults.reduce((acc, error) => acc + Math.pow(error - avgError, 2), 0) / this.testResults.length
    const errorStdDev = Math.sqrt(variance)
    
    // 计算同步准确度（基于误差大小）
    const avgAbsError = this.testResults.reduce((acc, error) => acc + Math.abs(error), 0) / this.testResults.length
    const syncAccuracy = Math.max(0, 100 - (avgAbsError / 10)) // 每10ms误差扣1分
    
    return {
      avgError,
      maxError,
      minError,
      errorStdDev,
      totalWords: this.testResults.length,
      syncAccuracy: Math.round(syncAccuracy * 100) / 100
    }
  }

  /**
   * 生成同步报告
   */
  public generateSyncReport(): string {
    const syncStatus = enhancedTTSService.getSyncStatus()
    const result = this.calculateTestResult()
    
    return `
=== TTS同步测试报告 ===
基础偏移量: ${syncStatus.baseOffset}ms
自适应偏移量: ${syncStatus.adaptiveOffset.toFixed(2)}ms
平均误差: ${result.avgError.toFixed(2)}ms
最大误差: ${result.maxError.toFixed(2)}ms
最小误差: ${result.minError.toFixed(2)}ms
误差标准差: ${result.errorStdDev.toFixed(2)}ms
测试单词数: ${result.totalWords}
同步准确度: ${result.syncAccuracy.toFixed(2)}%

建议:
${result.syncAccuracy > 95 ? '✅ 同步效果极佳，无需调整' :
  result.syncAccuracy > 85 ? '✅ 同步效果良好，可考虑微调' :
  result.syncAccuracy > 70 ? '⚠️ 同步效果一般，建议调整偏移量' :
  '❌ 同步效果较差，需要重新校准'}
    `.trim()
  }
}

// 导出单例实例
export const syncTester = new TTSSyncTester()

/**
 * 快速同步测试函数
 * @param text 测试文本
 * @returns 测试结果
 */
export async function quickSyncTest(text?: string): Promise<SyncTestResult> {
  const testText = text || "The quick brown fox jumps over the lazy dog. This is a synchronization test."
  return await syncTester.startSyncTest(testText)
}

/**
 * 一键优化同步效果
 * @param testText 用于校准的测试文本
 * @returns 优化结果
 */
export async function optimizeSync(testText?: string): Promise<{
  before: SyncTestResult
  after: SyncTestResult
  improvement: number
}> {
  const defaultText = testText || "Hello world, this is an automatic synchronization calibration test."
  
  // 测试当前效果
  const before = await syncTester.startSyncTest(defaultText)
  
  // 自动校准
  const calibrationResult = await syncTester.autoCalibrate(defaultText)
  
  // 测试优化后效果
  const after = await syncTester.startSyncTest(defaultText)
  
  const improvement = after.syncAccuracy - before.syncAccuracy
  
  return {
    before,
    after,
    improvement
  }
}
