import { ArrowLeft, Settings, Bookmark, Volume2 } from 'lucide-react'
import { BookData } from '../../pages/ReaderPage'
import { ReadingSettings } from './BookReader'

interface ReaderHeaderProps {
  book: BookData
  settings: ReadingSettings
  onBackToLibrary: () => void
  onToggleSettings: () => void
  onToggleBookmarks: () => void
  onToggleVoiceSelector: () => void
}

export default function ReaderHeader({
  book,
  settings,
  onBackToLibrary,
  onToggleSettings,
  onToggleBookmarks,
  onToggleVoiceSelector,
}: ReaderHeaderProps) {
  return (
    <div
      className={`sticky top-0 z-40 border-b backdrop-blur-sm ${
        settings.theme === 'dark'
          ? 'bg-gray-900/95 border-gray-700'
          : settings.theme === 'sepia'
            ? 'bg-amber-50/95 border-amber-200'
            : 'bg-white/95 border-gray-200'
      }`}
    >
      <div className="max-w-6xl mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          {/* 左侧：返回按钮和书籍信息 */}
          <div className="flex items-center space-x-4">
            <button
              onClick={onBackToLibrary}
              className={`p-2 rounded-lg transition-colors ${
                settings.theme === 'dark'
                  ? 'hover:bg-gray-800 text-gray-300'
                  : settings.theme === 'sepia'
                    ? 'hover:bg-amber-100 text-amber-700'
                    : 'hover:bg-gray-100 text-gray-600'
              }`}
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
            <div>
              <h1
                className={`text-lg font-semibold ${
                  settings.theme === 'dark'
                    ? 'text-white'
                    : settings.theme === 'sepia'
                      ? 'text-amber-900'
                      : 'text-gray-900'
                }`}
              >
                {book.title}
              </h1>
              {book.author && (
                <p
                  className={`text-sm ${
                    settings.theme === 'dark'
                      ? 'text-gray-400'
                      : settings.theme === 'sepia'
                        ? 'text-amber-600'
                        : 'text-gray-500'
                  }`}
                >
                  {book.author}
                </p>
              )}
            </div>
          </div>

          {/* 右侧：控制按钮 */}
          <div className="flex items-center space-x-2">
            <button
              onClick={onToggleVoiceSelector}
              className={`p-2 rounded-lg transition-colors ${
                settings.showVoiceSelector
                  ? settings.theme === 'dark'
                    ? 'bg-blue-600 text-white'
                    : settings.theme === 'sepia'
                      ? 'bg-orange-500 text-white'
                      : 'bg-blue-600 text-white'
                  : settings.theme === 'dark'
                    ? 'hover:bg-gray-800 text-gray-300'
                    : settings.theme === 'sepia'
                      ? 'hover:bg-amber-100 text-amber-700'
                      : 'hover:bg-gray-100 text-gray-600'
              }`}
              title="语音选择"
            >
              <Volume2 className="h-5 w-5" />
            </button>

            <button
              onClick={onToggleBookmarks}
              className={`p-2 rounded-lg transition-colors ${
                settings.showBookmarks
                  ? settings.theme === 'dark'
                    ? 'bg-blue-600 text-white'
                    : settings.theme === 'sepia'
                      ? 'bg-orange-500 text-white'
                      : 'bg-blue-600 text-white'
                  : settings.theme === 'dark'
                    ? 'hover:bg-gray-800 text-gray-300'
                    : settings.theme === 'sepia'
                      ? 'hover:bg-amber-100 text-amber-700'
                      : 'hover:bg-gray-100 text-gray-600'
              }`}
              title="书签"
            >
              <Bookmark className="h-5 w-5" />
            </button>

            <button
              onClick={onToggleSettings}
              className={`p-2 rounded-lg transition-colors ${
                settings.theme === 'dark'
                  ? 'hover:bg-gray-800 text-gray-300'
                  : settings.theme === 'sepia'
                    ? 'hover:bg-amber-100 text-amber-700'
                    : 'hover:bg-gray-100 text-gray-600'
              }`}
              title="设置"
            >
              <Settings className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
