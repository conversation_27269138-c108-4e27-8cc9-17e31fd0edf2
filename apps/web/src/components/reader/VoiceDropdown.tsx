import { TTSVoice } from '../../services/speech/EnhancedTTSService'

interface VoiceDropdownProps {
  voices: TTSVoice[]
  currentVoice: TTSVoice | null
  onVoiceChange: (voice: TTSVoice) => void
  theme: 'light' | 'dark' | 'sepia'
}

export default function VoiceDropdown({
  voices,
  currentVoice,
  onVoiceChange,
  theme,
}: VoiceDropdownProps) {
  if (!voices || voices.length === 0) {
    return <div className="text-xs opacity-70">加载中...</div>
  }

  // 分类语音：英语优先，其他语言放到"更多"
  const englishVoices = voices
    .filter(v => v.language.startsWith('en-') && v.provider === 'edge-tts')
    .sort((a, b) => {
      // 美国英语优先，然后英国英语
      if (a.language === 'en-US' && b.language !== 'en-US') return -1
      if (b.language === 'en-US' && a.language !== 'en-US') return 1
      if (
        a.language === 'en-GB' &&
        b.language !== 'en-GB' &&
        b.language !== 'en-US'
      )
        return -1
      if (
        b.language === 'en-GB' &&
        a.language !== 'en-GB' &&
        a.language !== 'en-US'
      )
        return 1
      return a.name.localeCompare(b.name)
    })

  const otherVoices = voices
    .filter(v => !v.language.startsWith('en-') || v.provider === 'browser')
    .sort((a, b) => {
      if (a.provider !== b.provider) return a.provider === 'edge-tts' ? -1 : 1
      if (a.language !== b.language) return a.language.localeCompare(b.language)
      return a.name.localeCompare(b.name)
    })

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const allVoices = [...englishVoices, ...otherVoices]
    const v = allVoices.find(v => v.id === e.target.value)
    if (v) onVoiceChange(v)
  }

  // 获取当前语音的简短显示名称
  const getCurrentVoiceDisplay = () => {
    if (!currentVoice) return '选择语音'

    if (currentVoice.language === 'en-US') {
      return currentVoice.name.includes('Aria')
        ? '美语 Aria'
        : currentVoice.name.includes('Jenny')
          ? '美语 Jenny'
          : currentVoice.name.includes('Guy')
            ? '美语 Guy'
            : '美语'
    }
    if (currentVoice.language === 'en-GB') {
      return currentVoice.name.includes('Sonia')
        ? '英语 Sonia'
        : currentVoice.name.includes('Ryan')
          ? '英语 Ryan'
          : '英语'
    }
    if (currentVoice.provider === 'browser') {
      return '系统默认'
    }

    // 其他语言显示语言代码
    const langMap: Record<string, string> = {
      'zh-CN': '中文',
      'zh-TW': '繁中',
      'ja-JP': '日语',
      'ko-KR': '韩语',
      'fr-FR': '法语',
      'de-DE': '德语',
      'es-ES': '西语',
      'it-IT': '意语',
      'pt-BR': '葡语',
      'ru-RU': '俄语',
    }
    return langMap[currentVoice.language] || currentVoice.language
  }

  return (
    <div className="flex items-center space-x-2">
      <span
        className={`text-sm ${
          theme === 'dark'
            ? 'text-gray-400'
            : theme === 'sepia'
              ? 'text-amber-700'
              : 'text-gray-500'
        }`}
      >
        语音
      </span>
      <select
        value={currentVoice?.id || ''}
        onChange={handleChange}
        className={`text-sm px-2 py-1 rounded border min-w-[100px] max-w-[120px] ${
          theme === 'dark'
            ? 'bg-gray-800 border-gray-600 text-gray-200'
            : theme === 'sepia'
              ? 'bg-amber-100 border-amber-300 text-amber-900'
              : 'bg-white border-gray-300 text-gray-800'
        }`}
        title={
          currentVoice
            ? `${currentVoice.name} (${currentVoice.language})`
            : '选择语音'
        }
      >
        {/* 英语语音 - 突出显示 */}
        <optgroup label="🇺🇸🇬🇧 英语">
          {englishVoices.map(v => (
            <option key={v.id} value={v.id}>
              {v.language === 'en-US' ? '🇺🇸 ' : '🇬🇧 '}
              {
                v.name
                  .replace('Microsoft ', '')
                  .replace(' Online (Natural)', '')
                  .split(' ')[0]
              }
            </option>
          ))}
        </optgroup>

        {/* 其他语音 */}
        {otherVoices.length > 0 && (
          <optgroup label="🌍 更多语言">
            {otherVoices
              .filter(v => v.provider === 'edge-tts')
              .map(v => (
                <option key={v.id} value={v.id}>
                  {v.name
                    .replace('Microsoft ', '')
                    .replace(' Online (Natural)', '')}{' '}
                  · {v.language}
                </option>
              ))}
            {otherVoices
              .filter(v => v.provider === 'browser')
              .map(v => (
                <option key={v.id} value={v.id}>
                  🌐 {v.name === '系统默认' ? '系统默认' : v.name}
                </option>
              ))}
          </optgroup>
        )}
      </select>
    </div>
  )
}
