import { useState, useEffect } from 'react'
import { 
  BookOpen, 
  Upload, 
  Search, 
  Clock, 
  Trash2, 
  FileText,
  Calendar,
  MoreVertical,
  Plus
} from 'lucide-react'
import { BookData } from '../../pages/ReaderPage'
import { bookService, BookMetadata } from '../../services/BookService'
import FileUpload from './FileUpload'

interface BookLibraryProps {
  onBookSelected: (book: BookData) => void
}

export default function BookLibrary({ onBookSelected }: BookLibraryProps) {
  const [books, setBooks] = useState<BookMetadata[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [showUpload, setShowUpload] = useState(false)
  const [selectedBook, setSelectedBook] = useState<string | null>(null)
  const [sortBy, setSortBy] = useState<'recent' | 'title' | 'date'>('recent')

  useEffect(() => {
    loadBooks()
  }, [])

  const loadBooks = () => {
    const allBooks = bookService.getBooks()
    setBooks(allBooks)
  }

  const handleBookUploaded = (book: BookData) => {
    bookService.saveBook(book)
    loadBooks()
    setShowUpload(false)
  }

  const handleBookSelect = (bookId: string) => {
    const book = bookService.getBook(bookId)
    if (book) {
      onBookSelected(book)
    }
  }

  const handleDeleteBook = (bookId: string, event: React.MouseEvent) => {
    event.stopPropagation()
    if (confirm('确定要删除这本书吗？此操作无法撤销。')) {
      bookService.deleteBook(bookId)
      loadBooks()
    }
  }

  const filteredBooks = books
    .filter(book => 
      book.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      book.fileName.toLowerCase().includes(searchQuery.toLowerCase())
    )
    .sort((a, b) => {
      switch (sortBy) {
        case 'recent':
          if (!a.lastReadAt && !b.lastReadAt) return b.uploadedAt.getTime() - a.uploadedAt.getTime()
          if (!a.lastReadAt) return 1
          if (!b.lastReadAt) return -1
          return b.lastReadAt.getTime() - a.lastReadAt.getTime()
        case 'title':
          return a.title.localeCompare(b.title)
        case 'date':
          return b.uploadedAt.getTime() - a.uploadedAt.getTime()
        default:
          return 0
      }
    })

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  const formatDate = (date: Date): string => {
    const now = new Date()
    const diffTime = now.getTime() - date.getTime()
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays === 0) return '今天'
    if (diffDays === 1) return '昨天'
    if (diffDays < 7) return `${diffDays}天前`
    if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`
    return date.toLocaleDateString('zh-CN')
  }

  if (showUpload) {
    return <FileUpload onBookUploaded={handleBookUploaded} />
  }

  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      {/* 头部 */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <BookOpen className="h-8 w-8 mr-3 text-blue-600" />
            我的书库
          </h1>
          <p className="text-gray-600 mt-2">
            共 {books.length} 本书籍 • {formatFileSize(books.reduce((sum, book) => sum + book.fileSize, 0))}
          </p>
        </div>
        
        <button
          onClick={() => setShowUpload(true)}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="h-5 w-5 mr-2" />
          上传书籍
        </button>
      </div>

      {/* 搜索和排序 */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <input
            type="text"
            placeholder="搜索书籍..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        
        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value as 'recent' | 'title' | 'date')}
          className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="recent">最近阅读</option>
          <option value="title">书名排序</option>
          <option value="date">上传时间</option>
        </select>
      </div>

      {/* 书籍列表 */}
      {filteredBooks.length === 0 ? (
        <div className="text-center py-12">
          {searchQuery ? (
            <div>
              <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">未找到匹配的书籍</h3>
              <p className="text-gray-500">尝试使用不同的关键词搜索</p>
            </div>
          ) : (
            <div>
              <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">还没有上传任何书籍</h3>
              <p className="text-gray-500 mb-4">上传您的第一本书开始阅读之旅</p>
              <button
                onClick={() => setShowUpload(true)}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Upload className="h-4 w-4 mr-2" />
                上传书籍
              </button>
            </div>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredBooks.map((book) => (
            <div
              key={book.id}
              className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow cursor-pointer group"
              onClick={() => handleBookSelect(book.id)}
            >
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900 mb-1 line-clamp-2 group-hover:text-blue-600 transition-colors">
                      {book.title}
                    </h3>
                    <p className="text-sm text-gray-500 mb-2">{book.fileName}</p>
                  </div>
                  
                  <div className="relative">
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        setSelectedBook(selectedBook === book.id ? null : book.id)
                      }}
                      className="p-1 rounded-full hover:bg-gray-100 transition-colors"
                    >
                      <MoreVertical className="h-4 w-4 text-gray-400" />
                    </button>
                    
                    {selectedBook === book.id && (
                      <div className="absolute right-0 top-8 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-10">
                        <button
                          onClick={(e) => handleDeleteBook(book.id, e)}
                          className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          删除
                        </button>
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex items-center text-sm text-gray-500">
                    <FileText className="h-4 w-4 mr-2" />
                    {book.totalParagraphs} 段落 • {formatFileSize(book.fileSize)}
                  </div>
                  
                  <div className="flex items-center text-sm text-gray-500">
                    <Calendar className="h-4 w-4 mr-2" />
                    上传于 {formatDate(book.uploadedAt)}
                  </div>
                  
                  {book.lastReadAt && (
                    <div className="flex items-center text-sm text-gray-500">
                      <Clock className="h-4 w-4 mr-2" />
                      {formatDate(book.lastReadAt)} 阅读
                    </div>
                  )}
                </div>

                {/* 阅读进度 */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">阅读进度</span>
                    <span className="text-gray-900 font-medium">{book.readingProgress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${book.readingProgress}%` }}
                    />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
