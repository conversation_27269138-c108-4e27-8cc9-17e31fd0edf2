import { useState, useRef } from 'react'
import { Upload, FileText, X, BookOpen } from 'lucide-react'
import { BookData } from '../../pages/ReaderPage'

interface FileUploadProps {
  onBookUploaded: (book: BookData) => void
}

export default function FileUpload({ onBookUploaded }: FileUploadProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileUpload(files[0])
    }
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      handleFileUpload(files[0])
    }
  }

  const handleFileUpload = async (file: File) => {
    setError(null)
    setIsUploading(true)

    try {
      // 验证文件类型
      if (!file.name.toLowerCase().endsWith('.txt')) {
        throw new Error('目前只支持 TXT 格式的文件')
      }

      // 验证文件大小 (最大 10MB)
      if (file.size > 10 * 1024 * 1024) {
        throw new Error('文件大小不能超过 10MB')
      }

      // 读取文件内容
      const content = await readFileContent(file)
      
      // 创建书籍数据
      const book: BookData = {
        id: generateId(),
        title: file.name.replace('.txt', ''),
        content: content,
        fileName: file.name,
        uploadedAt: new Date()
      }

      onBookUploaded(book)
    } catch (err) {
      setError(err instanceof Error ? err.message : '文件上传失败')
    } finally {
      setIsUploading(false)
    }
  }

  const readFileContent = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => {
        const content = e.target?.result as string
        if (!content || content.trim().length === 0) {
          reject(new Error('文件内容为空'))
          return
        }
        resolve(content)
      }
      reader.onerror = () => reject(new Error('文件读取失败'))
      reader.readAsText(file, 'UTF-8')
    })
  }

  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  const handleBrowseClick = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <div className="text-center mb-8">
        <BookOpen className="mx-auto h-16 w-16 text-blue-600 mb-4" />
        <h1 className="text-3xl font-bold text-gray-900 mb-2">语音阅读平台</h1>
        <p className="text-lg text-gray-600">
          上传您的书籍文件，开始智能语音阅读体验
        </p>
      </div>

      {/* 文件上传区域 */}
      <div
        className={`
          relative border-2 border-dashed rounded-lg p-8 text-center transition-colors
          ${isDragging 
            ? 'border-blue-400 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
          }
          ${isUploading ? 'pointer-events-none opacity-50' : ''}
        `}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept=".txt"
          onChange={handleFileSelect}
          className="hidden"
        />

        {isUploading ? (
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
            <p className="text-gray-600">正在处理文件...</p>
          </div>
        ) : (
          <div className="flex flex-col items-center">
            <Upload className="h-12 w-12 text-gray-400 mb-4" />
            <p className="text-xl font-medium text-gray-900 mb-2">
              拖拽文件到此处或点击上传
            </p>
            <p className="text-gray-500 mb-4">
              支持 TXT 格式，最大 10MB
            </p>
            <button
              onClick={handleBrowseClick}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <FileText className="h-4 w-4 mr-2" />
              选择文件
            </button>
          </div>
        )}
      </div>

      {/* 错误信息 */}
      {error && (
        <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
          <div className="flex items-center">
            <X className="h-5 w-5 text-red-400 mr-2" />
            <p className="text-red-800">{error}</p>
          </div>
        </div>
      )}

      {/* 支持的格式说明 */}
      <div className="mt-8 bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">支持的文件格式</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center p-3 bg-green-50 rounded-lg">
            <FileText className="h-6 w-6 text-green-600 mr-3" />
            <div>
              <p className="font-medium text-green-900">TXT</p>
              <p className="text-sm text-green-700">纯文本文件</p>
            </div>
          </div>
          <div className="flex items-center p-3 bg-gray-50 rounded-lg opacity-50">
            <FileText className="h-6 w-6 text-gray-400 mr-3" />
            <div>
              <p className="font-medium text-gray-500">EPUB</p>
              <p className="text-sm text-gray-400">即将支持</p>
            </div>
          </div>
          <div className="flex items-center p-3 bg-gray-50 rounded-lg opacity-50">
            <FileText className="h-6 w-6 text-gray-400 mr-3" />
            <div>
              <p className="font-medium text-gray-500">PDF</p>
              <p className="text-sm text-gray-400">即将支持</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
