import React, { useEffect, useRef, useState, useCallback } from 'react'
import { ReadingSettings, ReadingState } from './BookReader'
import {
  useVirtualScroll,
  VirtualParagraph,
} from '../../hooks/useVirtualScroll'

interface VirtualizedReadingAreaProps {
  paragraphs: string[]
  settings: ReadingSettings
  readingState: ReadingState
  onProgressChange: (paragraphIndex: number) => void
}

const ESTIMATED_PARAGRAPH_HEIGHT = 120 // 估算的段落高度
const BUFFER_SIZE = 3 // 缓冲区大小（前后各渲染3个段落）
const CONTAINER_HEIGHT = 600 // 容器高度

export default function VirtualizedReadingArea({
  paragraphs,
  settings,
  readingState,
  onProgressChange,
}: VirtualizedReadingAreaProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const scrollElementRef = useRef<HTMLDivElement>(null)
  const currentParagraphRef = useRef<HTMLDivElement>(null)
  const measurementCache = useRef<Map<number, number>>(new Map())

  const [scrollTop, setScrollTop] = useState(0)
  const [containerHeight, setContainerHeight] = useState(CONTAINER_HEIGHT)

  // 计算虚拟项目
  const virtualItems = useMemo(() => {
    const items: VirtualItem[] = []
    let offset = 0

    for (let i = 0; i < paragraphs.length; i++) {
      const height =
        measurementCache.current.get(i) || ESTIMATED_PARAGRAPH_HEIGHT
      items.push({
        index: i,
        height,
        offset,
      })
      offset += height
    }

    return items
  }, [paragraphs.length, measurementCache.current])

  // 计算可见范围
  const visibleRange = useMemo(() => {
    const start = Math.max(
      0,
      virtualItems.findIndex(item => item.offset + item.height > scrollTop) -
        BUFFER_SIZE
    )
    const end = Math.min(
      virtualItems.length - 1,
      virtualItems.findIndex(
        item => item.offset > scrollTop + containerHeight
      ) + BUFFER_SIZE
    )

    return {
      start: start === -1 ? 0 : start,
      end: end === -1 ? virtualItems.length - 1 : end,
    }
  }, [virtualItems, scrollTop, containerHeight])

  // 处理滚动
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop)
  }, [])

  // 更新容器高度
  useEffect(() => {
    const updateContainerHeight = () => {
      if (containerRef.current) {
        setContainerHeight(containerRef.current.clientHeight)
      }
    }

    updateContainerHeight()
    window.addEventListener('resize', updateContainerHeight)
    return () => window.removeEventListener('resize', updateContainerHeight)
  }, [])

  // 自动滚动到当前段落 - 位置偏上
  useEffect(() => {
    if (scrollElementRef.current && readingState.isPlaying) {
      const currentItem = virtualItems[readingState.currentParagraph]
      if (currentItem) {
        // 让当前段落显示在容器上方1/3处，而不是中心
        const targetScrollTop = currentItem.offset - containerHeight * 0.3
        scrollElementRef.current.scrollTo({
          top: Math.max(0, targetScrollTop),
          behavior: 'smooth',
        })
      }
    }
  }, [
    readingState.currentParagraph,
    readingState.isPlaying,
    virtualItems,
    containerHeight,
  ])

  // 分割句子
  const splitIntoSentences = (text: string): string[] => {
    // 改进的句子分割，避免过度分割，保持段落的自然流动
    const sentences = []
    const regex = /([^.!?。！？]*[.!?。！？]+(?:\s+|$))/g
    let match
    let lastIndex = 0

    while ((match = regex.exec(text)) !== null) {
      const sentence = match[1].trim()
      if (sentence.length > 0) {
        sentences.push(sentence)
      }
      lastIndex = regex.lastIndex
    }

    if (lastIndex < text.length) {
      const remaining = text.substring(lastIndex).trim()
      if (remaining) {
        sentences.push(remaining)
      }
    }

    // 如果没有找到明确的句子分割，将整个段落作为一个句子
    if (sentences.length === 0 && text.trim().length > 0) {
      sentences.push(text.trim())
    }

    return sentences.filter(s => s.length > 0)
  }

  // 分割单词
  const splitIntoWords = (text: string): string[] => {
    return text.split(/(\s+|[，。！？,.!?;:])/).filter(w => w.length > 0)
  }

  // 渲染段落
  const renderParagraph = useCallback(
    (paragraph: string, paragraphIndex: number) => {
      const isCurrentParagraph =
        paragraphIndex === readingState.currentParagraph
      const sentences = splitIntoSentences(paragraph)

      return (
        <div
          key={paragraphIndex}
          ref={isCurrentParagraph ? currentParagraphRef : null}
          className={`mb-6 p-4 rounded-lg transition-all duration-300 cursor-pointer ${
            settings.theme === 'dark'
              ? 'hover:bg-gray-800/50'
              : settings.theme === 'sepia'
                ? 'hover:bg-amber-100/30'
                : 'hover:bg-gray-50'
          }`}
          onClick={() => onProgressChange(paragraphIndex)}
          ref={element => {
            // 缓存实际高度
            if (element) {
              const height = element.offsetHeight
              if (
                height > 0 &&
                measurementCache.current.get(paragraphIndex) !== height
              ) {
                measurementCache.current.set(paragraphIndex, height)
              }
            }
          }}
        >
          {sentences.map((sentence, sentenceIndex) => {
            const isCurrentSentence =
              isCurrentParagraph &&
              sentenceIndex === readingState.currentSentence
            const words = splitIntoWords(sentence)

            return (
              <span
                key={sentenceIndex}
                className={`inline relative ${
                  isCurrentSentence && readingState.isPlaying
                    ? 'sentence-highlight-inline'
                    : ''
                }`}
              >
                {words.map((word, wordIndex) => {
                  const isCurrentWord =
                    isCurrentSentence &&
                    wordIndex === readingState.currentWord &&
                    readingState.isPlaying
                  const isSpace = /^\s+$/.test(word)
                  const isPunctuation = /^[，。！？,.!?;:]+$/.test(word)

                  if (isSpace) {
                    return <span key={wordIndex}> </span>
                  }

                  const nextWord = words[wordIndex + 1]
                  const needsSpace =
                    nextWord &&
                    !isPunctuation &&
                    !/^\s+$/.test(nextWord) &&
                    !/^[，。！？,.!?;:]+$/.test(nextWord)

                  return (
                    <React.Fragment key={wordIndex}>
                      <span className="reading-text">{word}</span>
                      {needsSpace && <span> </span>}
                    </React.Fragment>
                  )
                })}
              </span>
            )
          })}
        </div>
      )
    },
    [settings.theme, readingState, onProgressChange]
  )

  const totalHeight =
    virtualItems[virtualItems.length - 1]?.offset +
      virtualItems[virtualItems.length - 1]?.height || 0

  return (
    <div
      ref={containerRef}
      className={`prose max-w-none h-full overflow-hidden ${
        settings.theme === 'dark'
          ? 'prose-invert'
          : settings.theme === 'sepia'
            ? 'prose-amber'
            : ''
      }`}
      style={{
        fontSize: `${settings.fontSize}px`,
        lineHeight: settings.lineHeight,
        fontFamily: settings.fontFamily,
      }}
    >
      <div
        ref={scrollElementRef}
        className="h-full overflow-auto"
        onScroll={handleScroll}
      >
        <div style={{ height: totalHeight, position: 'relative' }}>
          <div
            className={`${
              settings.theme === 'dark'
                ? 'text-gray-100'
                : settings.theme === 'sepia'
                  ? 'text-amber-900'
                  : 'text-gray-900'
            }`}
          >
            {virtualItems
              .slice(visibleRange.start, visibleRange.end + 1)
              .map(item => (
                <div
                  key={item.index}
                  style={{
                    position: 'absolute',
                    top: item.offset,
                    width: '100%',
                  }}
                >
                  {renderParagraph(paragraphs[item.index], item.index)}
                </div>
              ))}
          </div>
        </div>
      </div>

      {/* 阅读进度指示器 */}
      <div
        className={`fixed bottom-20 right-4 p-3 rounded-lg shadow-lg ${
          settings.theme === 'dark'
            ? 'bg-gray-800 text-white'
            : settings.theme === 'sepia'
              ? 'bg-amber-100 text-amber-900'
              : 'bg-white text-gray-900'
        }`}
      >
        <div className="text-sm font-medium">
          {readingState.currentParagraph + 1} / {paragraphs.length}
        </div>
        <div className="text-xs opacity-75 mt-1">
          {Math.round(
            ((readingState.currentParagraph + 1) / paragraphs.length) * 100
          )}
          %
        </div>
      </div>
    </div>
  )
}
