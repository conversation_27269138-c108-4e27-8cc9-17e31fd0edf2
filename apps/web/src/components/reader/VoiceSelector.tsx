import { Volume2 } from 'lucide-react'
import { TTSVoice } from '../../services/speech/EnhancedTTSService'

interface VoiceSelectorProps {
  voices: TTSVoice[]
  currentVoice: TTSVoice | null
  onVoiceChange: (voice: TTSVoice) => void
  theme: 'light' | 'dark' | 'sepia'
}

export default function VoiceSelector({
  voices,
  currentVoice,
  onVoiceChange,
  theme,
}: VoiceSelectorProps) {
  // 分离微软语音和浏览器语音
  const edgeVoices = voices.filter(v => v.provider === 'edge-tts')
  const browserVoices = voices.filter(v => v.provider === 'browser')

  // 如果没有语音数据，显示加载状态
  if (voices.length === 0) {
    return (
      <div className="px-2 py-2">
        <div className="text-center text-sm opacity-70">
          正在加载语音选项...
        </div>
      </div>
    )
  }

  // 排序：先 Edge TTS，再 Browser，按语言+名称
  const sortedVoices = [...voices].sort((a, b) => {
    if (a.provider !== b.provider) {
      return a.provider === 'edge-tts' ? -1 : 1
    }
    if (a.language !== b.language) return a.language.localeCompare(b.language)
    return a.name.localeCompare(b.name)
  })

  const getLanguageName = (langCode: string) => {
    const names: Record<string, string> = {
      zh: '中文',
      en: 'English',
      ja: '日本語',
      ko: '한국어',
      fr: 'Français',
      de: 'Deutsch',
      es: 'Español',
      ru: 'Русский',
    }
    return names[langCode] || langCode.toUpperCase()
  }

  return (
    <div
      className={`border-t ${
        theme === 'dark'
          ? 'bg-gray-900/95 border-gray-700'
          : theme === 'sepia'
            ? 'bg-amber-50/95 border-amber-200'
            : 'bg-white/95 border-gray-200'
      } backdrop-blur-sm`}
    >
      <div className="max-w-6xl mx-auto px-4 py-2">
        {/* 语音选择器头部 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Volume2
              className={`h-4 w-4 mr-2 ${
                theme === 'dark'
                  ? 'text-gray-400'
                  : theme === 'sepia'
                    ? 'text-amber-700'
                    : 'text-gray-500'
              }`}
            />
            <span
              className={`text-sm font-medium ${
                theme === 'dark'
                  ? 'text-gray-300'
                  : theme === 'sepia'
                    ? 'text-amber-800'
                    : 'text-gray-700'
              }`}
            >
              选择语音
            </span>
            {currentVoice && (
              <span
                className={`ml-2 text-xs ${
                  theme === 'dark'
                    ? 'text-gray-500'
                    : theme === 'sepia'
                      ? 'text-amber-600'
                      : 'text-gray-400'
                }`}
              >
                {currentVoice.name} (
                {currentVoice.provider === 'edge-tts' ? 'Microsoft' : '浏览器'})
              </span>
            )}
          </div>

          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className={`p-1 rounded transition-colors ${
              theme === 'dark'
                ? 'hover:bg-gray-800 text-gray-400'
                : theme === 'sepia'
                  ? 'hover:bg-amber-100 text-amber-700'
                  : 'hover:bg-gray-100 text-gray-500'
            }`}
          >
            {isExpanded ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </button>
        </div>

        {/* 展开的语音列表 */}
        {isExpanded && (
          <div className="mt-2 pt-2 border-t border-opacity-20">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 max-h-48 overflow-y-auto">
              {/* 微软语音 */}
              {Object.entries(groupedEdgeVoices).map(
                ([langCode, langVoices]) => (
                  <div key={langCode} className="space-y-2">
                    <h4
                      className={`text-xs font-medium ${
                        theme === 'dark'
                          ? 'text-gray-400'
                          : theme === 'sepia'
                            ? 'text-amber-700'
                            : 'text-gray-600'
                      }`}
                    >
                      🎤 Microsoft {getLanguageName(langCode)} (
                      {langVoices.length})
                    </h4>
                    <div className="space-y-1">
                      {langVoices.slice(0, 2).map(voice => (
                        <button
                          key={voice.id}
                          onClick={() => onVoiceChange(voice)}
                          className={`w-full text-left p-2 rounded text-xs transition-colors ${
                            currentVoice?.id === voice.id
                              ? theme === 'dark'
                                ? 'bg-blue-600 text-white'
                                : theme === 'sepia'
                                  ? 'bg-orange-500 text-white'
                                  : 'bg-blue-600 text-white'
                              : theme === 'dark'
                                ? 'hover:bg-gray-800 text-gray-300 bg-gray-700/50'
                                : theme === 'sepia'
                                  ? 'hover:bg-amber-200 text-amber-900 bg-amber-100/50'
                                  : 'hover:bg-gray-100 text-gray-700 bg-gray-50'
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="font-medium">
                                {voice.name
                                  .replace('Microsoft ', '')
                                  .replace(' Online (Natural)', '')}
                              </p>
                              <p
                                className={`text-xs opacity-75 ${
                                  currentVoice?.id === voice.id
                                    ? 'text-white'
                                    : ''
                                }`}
                              >
                                {voice.language} • {voice.gender}
                              </p>
                            </div>
                            {currentVoice?.id === voice.id && (
                              <div className="w-2 h-2 bg-white rounded-full"></div>
                            )}
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                )
              )}

              {/* 浏览器语音 - 简化显示 */}
              <div className="space-y-2">
                <h4
                  className={`text-xs font-medium ${
                    theme === 'dark'
                      ? 'text-gray-400'
                      : theme === 'sepia'
                        ? 'text-amber-700'
                        : 'text-gray-600'
                  }`}
                >
                  🌐 浏览器内置
                </h4>
                <button
                  onClick={() => {
                    // 选择第一个浏览器语音或创建一个默认的
                    const browserVoice = browserVoices[0] || {
                      id: 'browser-default',
                      name: '系统默认',
                      language: 'auto',
                      gender: 'Unknown',
                      provider: 'browser' as const,
                    }
                    onVoiceChange(browserVoice)
                  }}
                  className={`w-full text-left p-2 rounded text-xs transition-colors ${
                    currentVoice?.provider === 'browser'
                      ? theme === 'dark'
                        ? 'bg-blue-600 text-white'
                        : theme === 'sepia'
                          ? 'bg-orange-500 text-white'
                          : 'bg-blue-600 text-white'
                      : theme === 'dark'
                        ? 'hover:bg-gray-800 text-gray-300 bg-gray-700/50'
                        : theme === 'sepia'
                          ? 'hover:bg-amber-200 text-amber-900 bg-amber-100/50'
                          : 'hover:bg-gray-100 text-gray-700 bg-gray-50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">系统默认语音</p>
                      <p
                        className={`text-xs opacity-75 ${
                          currentVoice?.provider === 'browser'
                            ? 'text-white'
                            : ''
                        }`}
                      >
                        浏览器自动选择 • 快速响应
                      </p>
                    </div>
                    {currentVoice?.provider === 'browser' && (
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    )}
                  </div>
                </button>
              </div>
            </div>

            {/* 提示信息 */}
            <div
              className={`mt-2 p-2 rounded text-xs ${
                theme === 'dark'
                  ? 'bg-gray-800 text-gray-400'
                  : theme === 'sepia'
                    ? 'bg-amber-100 text-amber-700'
                    : 'bg-gray-50 text-gray-600'
              }`}
            >
              💡 Microsoft语音音质更佳，浏览器语音响应更快
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
