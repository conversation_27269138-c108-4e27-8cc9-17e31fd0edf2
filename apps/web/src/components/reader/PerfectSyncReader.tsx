import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react'
import { perfectSyncTTS } from '../../services/speech/PerfectSyncTTSService'
import type {
  PerfectSyncEvent,
  PerfectSyncWord,
  SyncCalibrationData,
} from '../../services/speech/PerfectSyncTTSService'
import { SyncDebugPanel } from '../debug/SyncDebugPanel'

interface PerfectSyncReaderProps {
  text: string
  onWordHighlight?: (wordIndex: number, charIndex: number) => void
}

export const PerfectSyncReader: React.FC<PerfectSyncReaderProps> = ({
  text,
  onWordHighlight,
}) => {
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentWordIndex, setCurrentWordIndex] = useState(-1)
  const [showDebugPanel, setShowDebugPanel] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [words, setWords] = useState<PerfectSyncWord[]>([])
  const [calibrationData, setCalibrationData] =
    useState<SyncCalibrationData | null>(null)
  const [syncDrift, setSyncDrift] = useState<number>(0)

  // 用于存储单词元素引用
  const wordRefs = useRef<Map<number, HTMLSpanElement>>(new Map())
  const containerRef = useRef<HTMLDivElement>(null)

  // 初始化TTS服务
  useEffect(() => {
    const initializeService = async () => {
      setIsLoading(true)
      try {
        await perfectSyncTTS.loadAndPrepare(text)
        const syncWords = perfectSyncTTS.getWords()
        setWords(syncWords)
      } catch (error) {
        console.error('Failed to initialize TTS:', error)
      } finally {
        setIsLoading(false)
      }
    }

    initializeService()

    return () => {
      perfectSyncTTS.stop()
    }
  }, [text])

  // 监听TTS事件
  useEffect(() => {
    const handleTTSEvent = (event: PerfectSyncEvent) => {
      switch (event.type) {
        case 'start':
          setIsPlaying(true)
          break
        case 'end':
        case 'pause':
          setIsPlaying(false)
          break
        case 'word-highlight':
          if (event.data?.wordIndex !== undefined && event.data?.word) {
            setCurrentWordIndex(event.data.wordIndex)
            onWordHighlight?.(event.data.wordIndex, event.data.word.charStart)
            scrollToWord(event.data.wordIndex)
          }
          break
        case 'calibration-update':
          if (event.data?.calibration) {
            setCalibrationData(event.data.calibration)
          }
          break
        case 'sync-drift-detected':
          if (event.data?.driftMs) {
            setSyncDrift(event.data.driftMs)
          }
          break
      }
    }

    perfectSyncTTS.addEventListener(handleTTSEvent)
    return () => perfectSyncTTS.removeEventListener(handleTTSEvent)
  }, [onWordHighlight])

  // 滚动到当前单词
  const scrollToWord = useCallback((wordIndex: number) => {
    const wordElement = wordRefs.current.get(wordIndex)
    if (wordElement && containerRef.current) {
      const containerRect = containerRef.current.getBoundingClientRect()
      const wordRect = wordElement.getBoundingClientRect()

      const isInView =
        wordRect.top >= containerRect.top &&
        wordRect.bottom <= containerRect.bottom

      if (!isInView) {
        // 使用自定义滚动位置，让单词显示在屏幕上方1/3处
        const wordRect = wordElement.getBoundingClientRect()
        const viewportHeight = window.innerHeight
        const targetOffset = viewportHeight * 0.3
        const currentScrollY = window.pageYOffset
        const wordTop = wordRect.top + currentScrollY
        const targetScrollY = wordTop - targetOffset

        window.scrollTo({
          top: Math.max(0, targetScrollY),
          behavior: 'smooth',
        })
      }
    }
  }, [])

  const handlePlay = async () => {
    if (isPlaying) {
      perfectSyncTTS.pause()
    } else {
      await perfectSyncTTS.play()
    }
  }

  const handleStop = () => {
    perfectSyncTTS.stop()
    setCurrentWordIndex(-1)
  }

  const handleOptimizeSync = () => {
    // 手动调整延迟补偿
    const currentCompensation =
      perfectSyncTTS.getCalibrationData().measuredLatency
    const newCompensation = prompt(
      `当前延迟补偿: ${currentCompensation}ms\n请输入新的延迟补偿值（毫秒）：`,
      String(currentCompensation)
    )

    if (newCompensation) {
      const value = parseInt(newCompensation, 10)
      if (!isNaN(value)) {
        perfectSyncTTS.setLatencyCompensation(value)
        alert(`延迟补偿已设置为 ${value}ms`)
      }
    }
  }

  return (
    <div className="perfect-sync-reader">
      {/* 控制面板 */}
      <div className="mb-4 flex items-center gap-3">
        <button
          onClick={handlePlay}
          className={`px-4 py-2 rounded-lg font-medium ${
            isPlaying
              ? 'bg-red-600 text-white hover:bg-red-700'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {isPlaying ? '暂停' : '播放'}
        </button>

        <button
          onClick={handleStop}
          className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
        >
          停止
        </button>

        <button
          onClick={handleOptimizeSync}
          className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
          title="调整延迟补偿"
        >
          调整延迟
        </button>

        <button
          onClick={() => setShowDebugPanel(true)}
          className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
          title="打开同步调试面板"
        >
          调试面板
        </button>

        {calibrationData && (
          <span className="text-green-600 text-sm font-medium">
            延迟: {calibrationData.measuredLatency.toFixed(0)}ms
          </span>
        )}

        {syncDrift !== 0 && (
          <span className="text-yellow-600 text-sm font-medium">
            偏移: {syncDrift.toFixed(0)}ms
          </span>
        )}
      </div>

      {/* 文本显示区域 */}
      <div
        ref={containerRef}
        className="text-display p-6 bg-gray-50 rounded-lg leading-relaxed max-h-96 overflow-y-auto"
      >
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          words.map((word, index) => (
            <span
              key={index}
              ref={el => {
                if (el) wordRefs.current.set(index, el)
              }}
              className={`inline-block mr-2 mb-1 px-1 py-0.5 rounded transition-all duration-150 cursor-pointer ${
                index === currentWordIndex
                  ? 'bg-blue-600 text-white font-medium shadow-lg transform scale-105'
                  : isPlaying && index < currentWordIndex
                    ? 'bg-yellow-200 text-gray-800'
                    : 'text-gray-800 hover:bg-gray-200'
              }`}
              style={{
                // 添加平滑的高亮动画
                transition:
                  index === currentWordIndex
                    ? 'all 0.1s cubic-bezier(0.4, 0, 0.2, 1)'
                    : 'all 0.2s ease-out',
              }}
              onClick={() => {
                perfectSyncTTS.jumpToWord(index)
                setCurrentWordIndex(index)
              }}
            >
              {word.text}
            </span>
          ))
        )}
      </div>

      {/* 同步状态指示器 */}
      {isPlaying && (
        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse"></div>
              <span className="text-sm text-blue-800">
                正在朗读... 当前单词:{' '}
                {currentWordIndex >= 0 && words[currentWordIndex]
                  ? words[currentWordIndex].text
                  : ''}
              </span>
            </div>
            <div className="text-xs text-blue-600">
              进度: {currentWordIndex + 1} / {words.length}
            </div>
          </div>
        </div>
      )}

      {/* 使用提示 */}
      <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
        <h4 className="font-semibold text-yellow-800 mb-2">
          完美同步功能说明:
        </h4>
        <ul className="text-sm text-yellow-700 space-y-1">
          <li>
            • <strong>自动优化:</strong> 首次播放前会自动校准同步参数
          </li>
          <li>
            • <strong>实时调整:</strong> 播放过程中会根据实际情况动态调整
          </li>
          <li>
            • <strong>高频同步:</strong> 每5ms检查一次，确保精确同步
          </li>
          <li>
            • <strong>预测性高亮:</strong> 提前预测下一个单词，减少延迟
          </li>
          <li>
            • <strong>调试面板:</strong> 可以手动调整参数和查看同步状态
          </li>
        </ul>
      </div>

      {/* 调试面板 */}
      <SyncDebugPanel
        isOpen={showDebugPanel}
        onClose={() => setShowDebugPanel(false)}
      />
    </div>
  )
}

// 使用示例组件
export const PerfectSyncReaderDemo: React.FC = () => {
  const sampleText = `
    The quick brown fox jumps over the lazy dog. This is a demonstration of perfect synchronization 
    between text-to-speech audio and word highlighting. The system uses advanced algorithms to ensure 
    frame-perfect timing, with automatic calibration and real-time adjustments. Every word should be 
    highlighted at exactly the right moment when it's being spoken.
  `.trim()

  const [highlightedWord, setHighlightedWord] = useState<{
    index: number
    char: number
  } | null>(null)

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">完美同步TTS阅读器演示</h1>

      <PerfectSyncReader
        text={sampleText}
        onWordHighlight={(wordIndex, charIndex) => {
          setHighlightedWord({ index: wordIndex, char: charIndex })
        }}
      />

      {highlightedWord && (
        <div className="mt-4 p-3 bg-gray-100 rounded-lg">
          <div className="text-sm text-gray-600">
            当前高亮: 单词索引 {highlightedWord.index}, 字符位置{' '}
            {highlightedWord.char}
          </div>
        </div>
      )}
    </div>
  )
}
