import { useState, useEffect, useCallback, useRef } from 'react'
import { ArrowLeft, Settings, Bookmark, Languages } from 'lucide-react'
import { BookData } from '../../pages/ReaderPage'
import OptimizedReadingArea from './OptimizedReadingArea'
import PlayerControls from './PlayerControls'
import TranslationPanel from './TranslationPanel'
import BookmarkPanel from './BookmarkPanel'
import {
  readingService,
  ReadingProgress,
} from '../../services/speech/ReadingService'
import { bookService, BookmarkData } from '../../services/BookService'
import { TTSVoice } from '../../services/speech/EnhancedTTSService'
import {
  defaultTranslationManager,
  ParagraphTranslation,
  TranslationSettings,
} from '../../services/translation/TranslationManager'

interface BookReaderProps {
  book: BookData
  onBackToLibrary: () => void
}

// 根据内容语言选择默认语音
function selectDefaultVoiceForContent(
  voices: TTSVoice[],
  content: string
): TTSVoice | null {
  // 检测内容主要语言
  const isEnglish =
    /[a-zA-Z]/.test(content) &&
    (content.match(/[a-zA-Z]/g) || []).length >
      (content.match(/[\u4e00-\u9fff]/g) || []).length

  const isChinese =
    /[\u4e00-\u9fff]/.test(content) &&
    (content.match(/[\u4e00-\u9fff]/g) || []).length >
      (content.match(/[a-zA-Z]/g) || []).length

  if (isEnglish) {
    // 英文内容：优先选择美国Jenny语音
    const jennyVoice = voices.find(
      v =>
        v.provider === 'edge-tts' &&
        v.language === 'en-US' &&
        v.name.includes('Jenny')
    )
    if (jennyVoice) {
      console.log('🎤 检测到英文内容，选择美国Jenny语音')
      return jennyVoice
    }

    // 如果没有Jenny，选择其他美国英语语音
    const usEnglishVoice = voices.find(
      v => v.provider === 'edge-tts' && v.language === 'en-US'
    )
    if (usEnglishVoice) {
      console.log('🎤 检测到英文内容，选择美国英语语音:', usEnglishVoice.name)
      return usEnglishVoice
    }

    // 回退到任何英语语音
    const anyEnglishVoice = voices.find(
      v => v.provider === 'edge-tts' && v.language.startsWith('en-')
    )
    if (anyEnglishVoice) {
      console.log('🎤 检测到英文内容，选择英语语音:', anyEnglishVoice.name)
      return anyEnglishVoice
    }
  }

  if (isChinese) {
    // 中文内容：选择中文语音
    const chineseVoice = voices.find(
      v => v.provider === 'edge-tts' && v.language.startsWith('zh')
    )
    if (chineseVoice) {
      console.log('🎤 检测到中文内容，选择中文语音:', chineseVoice.name)
      return chineseVoice
    }
  }

  // 默认回退：选择第一个Edge TTS语音或任何可用语音
  const defaultVoice = voices.find(v => v.provider === 'edge-tts') || voices[0]
  if (defaultVoice) {
    console.log('🎤 使用默认语音:', defaultVoice.name)
  }
  return defaultVoice || null
}

export interface ReadingSettings {
  fontSize: number
  lineHeight: number
  fontFamily: string
  theme: 'light' | 'dark' | 'sepia'
  showTranslation: boolean
  translationLanguage: string
  showBookmarks: boolean
}

export interface ReadingState {
  currentParagraph: number
  currentSentence: number
  currentWord: number
  isPlaying: boolean
  playbackRate: number
  volume: number
  progress: ReadingProgress | null
}

export default function BookReader({ book, onBackToLibrary }: BookReaderProps) {
  const [settings, setSettings] = useState<ReadingSettings>({
    fontSize: 16,
    lineHeight: 1.6,
    fontFamily: 'Inter',
    theme: 'light',
    showTranslation: false,
    translationLanguage: 'zh',
    showBookmarks: false,
  })

  const [readingState, setReadingState] = useState<ReadingState>({
    currentParagraph: 0,
    currentSentence: 0,
    currentWord: 0,
    isPlaying: false,
    playbackRate: 1.0,
    volume: 1.0,
    progress: null,
  })

  const [showSettings, setShowSettings] = useState(false)
  const [paragraphs, setParagraphs] = useState<string[]>([])
  const [bookmarks, setBookmarks] = useState<BookmarkData[]>([])
  const [availableVoices, setAvailableVoices] = useState<TTSVoice[]>([])
  const [currentVoice, setCurrentVoice] = useState<TTSVoice | null>(null)
  const [showBackToReading, setShowBackToReading] = useState(false)
  const currentVoiceRef = useRef<TTSVoice | null>(null)
  const userScrollingRef = useRef(false) // 跟踪用户是否在手动滚动

  // 翻译相关状态
  const [translations, setTranslations] = useState<
    Map<number, ParagraphTranslation>
  >(new Map())
  const [translationSettings, setTranslationSettings] =
    useState<TranslationSettings>({
      enabled: false,
      targetLanguage: 'zh',
      provider: 'google',
      autoTranslate: true,
      visibleRangeBuffer: 3,
    })

  // 同步ref和state
  useEffect(() => {
    currentVoiceRef.current = currentVoice
  }, [currentVoice])

  // 加载可用语音 - 使用ref避免依赖循环
  const loadAvailableVoices = useCallback(async () => {
    try {
      const voices = await readingService.getAllVoices()
      setAvailableVoices(voices)

      // 只在没有当前语音时才设置默认语音
      if (!currentVoiceRef.current && voices.length > 0) {
        // 尝试从localStorage加载保存的语音
        let selectedVoice: TTSVoice | null = null

        try {
          const savedVoiceStr = localStorage.getItem('preferredVoice')
          if (savedVoiceStr) {
            const savedVoice = JSON.parse(savedVoiceStr) as TTSVoice
            // 验证保存的语音是否仍然可用
            selectedVoice =
              voices.find(
                v =>
                  v.provider === savedVoice.provider &&
                  (v.provider === 'edge-tts'
                    ? v.edgeVoiceId === savedVoice.edgeVoiceId
                    : v.nativeVoice?.name === savedVoice.nativeVoice?.name)
              ) || null
          }
        } catch (error) {
          console.warn('Failed to load saved voice preference:', error)
        }

        // 如果没有保存的语音或保存的语音不可用，根据内容语言选择默认语音
        if (!selectedVoice) {
          selectedVoice = selectDefaultVoiceForContent(voices, book.content)
        }

        if (selectedVoice) {
          setCurrentVoice(selectedVoice)
          await readingService.setVoice(selectedVoice)
        }
      }
    } catch (error) {
      console.error('Failed to load voices:', error)
    }
  }, [book.content]) // 依赖book.content来根据内容选择合适的默认语音

  // 解析文本内容为段落并初始化阅读服务
  useEffect(() => {
    const parsedParagraphs = book.content
      .split(/\n\s*\n/)
      .map(p => p.trim())
      .filter(p => p.length > 0)

    setParagraphs(parsedParagraphs)

    // 加载书签和阅读进度
    const bookMetadata = bookService.getBooks().find(b => b.id === book.id)
    if (bookMetadata) {
      setBookmarks(bookMetadata.bookmarks)
    }

    // 初始化阅读服务
    readingService.loadText(book.content)
    readingService.updateOptions({
      rate: readingState.playbackRate,
      volume: readingState.volume,
      lang: 'en-US', // 可以根据内容自动检测
    })
  }, [book.content, book.id, readingState.playbackRate, readingState.volume])

  // 单独的useEffect来加载语音，避免依赖循环
  useEffect(() => {
    loadAvailableVoices()
  }, [loadAvailableVoices])

  // 初始化翻译管理器
  useEffect(() => {
    // 设置段落文本
    defaultTranslationManager.setParagraphs(paragraphs)

    // 同步翻译设置
    const updatedSettings = {
      ...translationSettings,
      enabled: settings.showTranslation,
      targetLanguage: settings.translationLanguage,
    }
    defaultTranslationManager.updateSettings(updatedSettings)

    // 监听翻译更新
    const handleTranslationUpdate = (
      newTranslations: Map<number, ParagraphTranslation>
    ) => {
      setTranslations(new Map(newTranslations))
    }

    defaultTranslationManager.addEventListener(handleTranslationUpdate)

    return () => {
      defaultTranslationManager.removeEventListener(handleTranslationUpdate)
    }
  }, [
    paragraphs,
    settings.showTranslation,
    settings.translationLanguage,
    translationSettings,
  ])

  // 监听翻译设置变化
  useEffect(() => {
    const updatedSettings = {
      ...translationSettings,
      enabled: settings.showTranslation,
      targetLanguage: settings.translationLanguage,
    }
    setTranslationSettings(updatedSettings)
    defaultTranslationManager.updateSettings(updatedSettings)
  }, [
    settings.showTranslation,
    settings.translationLanguage,
    translationSettings,
  ])

  // 单独的useEffect来恢复阅读位置，确保在段落加载完成后执行
  useEffect(() => {
    if (paragraphs.length > 0) {
      // 尝试加载详细的阅读位置
      try {
        const detailedPositionStr = localStorage.getItem(
          `reading-position-${book.id}`
        )
        if (detailedPositionStr) {
          const detailedPosition = JSON.parse(detailedPositionStr)
          const restoredPosition = {
            paragraphIndex: detailedPosition.paragraphIndex,
            sentenceIndex: detailedPosition.sentenceIndex || 0,
            wordIndex: detailedPosition.wordIndex || 0,
          }

          // 验证位置有效性
          if (
            restoredPosition.paragraphIndex > 0 &&
            restoredPosition.paragraphIndex < paragraphs.length
          ) {
            console.log('恢复阅读位置:', restoredPosition)

            setReadingState(prev => ({
              ...prev,
              currentParagraph: restoredPosition.paragraphIndex,
              currentSentence: restoredPosition.sentenceIndex,
              currentWord: restoredPosition.wordIndex,
            }))

            // 跳转到恢复的位置
            setTimeout(() => {
              readingService.jumpTo(restoredPosition)

              // 使用估算位置滚动，因为虚拟化可能导致元素未渲染
              setTimeout(() => {
                const estimatedParagraphHeight = 120
                const estimatedPosition =
                  restoredPosition.paragraphIndex * estimatedParagraphHeight
                const viewportHeight = window.innerHeight
                const targetScrollY = estimatedPosition - viewportHeight * 0.3

                window.scrollTo({
                  top: Math.max(0, targetScrollY),
                  behavior: 'smooth',
                })

                // 等待滚动和渲染完成后，尝试精确定位
                setTimeout(() => {
                  const element = document.querySelector(
                    `[data-paragraph-index="${restoredPosition.paragraphIndex}"]`
                  )
                  if (element) {
                    element.scrollIntoView({
                      behavior: 'smooth',
                      block: 'start',
                    })
                  }
                }, 1000)
              }, 200)
            }, 500)
          }
        }
      } catch (error) {
        console.warn('Failed to restore reading position:', error)
      }
    }
  }, [paragraphs.length, book.id]) // 只在段落加载完成时执行一次

  // 监听滚动事件，显示/隐藏返回朗读位置按钮
  useEffect(() => {
    let scrollTimeout: NodeJS.Timeout | null = null

    const handleScroll = () => {
      // 清除之前的定时器
      if (scrollTimeout) {
        clearTimeout(scrollTimeout)
      }

      // 延迟检查，避免滚动过程中频繁更新
      scrollTimeout = setTimeout(() => {
        // 只有在朗读时才可能显示返回按钮
        if (readingState.isPlaying) {
          // 优先尝试查找当前句子元素
          const currentSentenceElement = document.querySelector(
            `[data-paragraph="${readingState.currentParagraph}"][data-sentence="${readingState.currentSentence}"]`
          )

          if (currentSentenceElement) {
            // 如果找到句子元素，检查句子是否在合适的视口位置
            const rect = currentSentenceElement.getBoundingClientRect()
            const viewportHeight = window.innerHeight

            // 检查句子是否在视口的中心区域（上方20%到下方80%）
            const isInCenterArea =
              rect.top >= viewportHeight * 0.2 &&
              rect.bottom <= viewportHeight * 0.8

            setShowBackToReading(!isInCenterArea)
          } else {
            // 如果找不到句子，尝试查找段落
            const currentParagraphElement = document.querySelector(
              `[data-paragraph-index="${readingState.currentParagraph}"]`
            )

            if (currentParagraphElement) {
              const rect = currentParagraphElement.getBoundingClientRect()
              const viewportHeight = window.innerHeight
              const isInGoodPosition =
                rect.top >= 0 &&
                rect.top < viewportHeight * 0.6 &&
                rect.bottom > viewportHeight * 0.1

              setShowBackToReading(!isInGoodPosition)
            } else {
              // 如果段落也找不到，说明当前位置不在视口中，显示返回按钮
              setShowBackToReading(true)
            }
          }
        } else {
          setShowBackToReading(false)
        }
      }, 200) // 增加延迟，避免与自动滚动冲突
    }

    window.addEventListener('scroll', handleScroll, { passive: true })

    // 初始检查
    handleScroll()

    return () => {
      window.removeEventListener('scroll', handleScroll)
      if (scrollTimeout) {
        clearTimeout(scrollTimeout)
      }
    }
  }, [
    readingState.isPlaying,
    readingState.currentParagraph,
    readingState.currentSentence,
  ])

  // 监听阅读进度
  const handleReadingProgress = useCallback(
    (progress: ReadingProgress) => {
      setReadingState(prev => ({
        ...prev,
        currentParagraph: progress.position.paragraphIndex,
        currentSentence: progress.position.sentenceIndex,
        currentWord: progress.position.wordIndex,
        progress,
      }))

      // 更新当前语音
      if (progress.currentVoice && progress.currentVoice !== currentVoice) {
        setCurrentVoice(progress.currentVoice)
      }

      // 保存阅读进度到本地存储（包括详细位置信息）
      const readingProgressPercent = Math.round(
        (progress.position.paragraphIndex /
          Math.max(1, progress.totalParagraphs)) *
          100
      )

      bookService.updateReadingProgress(book.id, {
        currentParagraph: progress.position.paragraphIndex,
        readingProgress: readingProgressPercent,
      })

      // 保存详细的阅读位置到localStorage
      const detailedPosition = {
        paragraphIndex: progress.position.paragraphIndex,
        sentenceIndex: progress.position.sentenceIndex,
        wordIndex: progress.position.wordIndex,
        timestamp: Date.now(),
      }
      localStorage.setItem(
        `reading-position-${book.id}`,
        JSON.stringify(detailedPosition)
      )
    },
    [book.id, currentVoice]
  )

  useEffect(() => {
    readingService.addEventListener(handleReadingProgress)
    return () => {
      readingService.removeEventListener(handleReadingProgress)
    }
  }, [handleReadingProgress])

  // 键盘快捷键
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      // 只在阅读器页面激活时响应快捷键
      if (
        event.target instanceof HTMLInputElement ||
        event.target instanceof HTMLTextAreaElement
      ) {
        return
      }

      switch (event.code) {
        case 'Space':
          event.preventDefault()
          handlePlayPause()
          break
        case 'Escape':
          event.preventDefault()
          handleStop()
          break
        case 'ArrowLeft':
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault()
            if (readingState.currentParagraph > 0) {
              handleProgressChange(readingState.currentParagraph - 1)
            }
          }
          break
        case 'ArrowRight':
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault()
            if (readingState.currentParagraph < paragraphs.length - 1) {
              handleProgressChange(readingState.currentParagraph + 1)
            }
          }
          break
        case 'ArrowUp':
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault()
            const newSpeed = Math.min(2.0, readingState.playbackRate + 0.25)
            handleSpeedChange(newSpeed)
          }
          break
        case 'ArrowDown':
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault()
            const newSpeed = Math.max(0.5, readingState.playbackRate - 0.25)
            handleSpeedChange(newSpeed)
          }
          break
        case 'KeyT':
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault()
            handleSettingsChange({ showTranslation: !settings.showTranslation })
          }
          break
      }
    }

    document.addEventListener('keydown', handleKeyPress)
    return () => {
      document.removeEventListener('keydown', handleKeyPress)
    }
  }, [readingState, settings, paragraphs.length])

  // 清理资源
  useEffect(() => {
    return () => {
      readingService.destroy()
      defaultTranslationManager.destroy()
    }
  }, [])

  const handlePlayPause = () => {
    const isCurrentlyPlaying =
      readingService.getIsReading() && !readingService.getIsPaused()

    if (isCurrentlyPlaying) {
      readingService.pause()
    } else {
      readingService.start()
    }

    setReadingState(prev => ({
      ...prev,
      isPlaying: !isCurrentlyPlaying,
    }))
  }

  const handleStop = () => {
    readingService.stop()
    setReadingState(prev => ({
      ...prev,
      isPlaying: false,
      currentParagraph: 0,
      currentSentence: 0,
      currentWord: 0,
    }))
  }

  const handleSpeedChange = (speed: number) => {
    readingService.updateOptions({ rate: speed })
    setReadingState(prev => ({
      ...prev,
      playbackRate: speed,
    }))
  }

  const handleVolumeChange = (volume: number) => {
    readingService.updateOptions({ volume })
    setReadingState(prev => ({
      ...prev,
      volume,
    }))
  }

  const handleProgressChange = (paragraphIndex: number) => {
    // 检查是否之前在播放
    const wasReading =
      readingService.getIsReading() && !readingService.getIsPaused()

    // 先停止当前朗读，避免同时朗读多个位置
    readingService.forceStopKeepPosition()

    // 跳转到新位置
    readingService.jumpTo({ paragraphIndex, sentenceIndex: 0, wordIndex: 0 })

    // 更新状态
    setReadingState(prev => ({
      ...prev,
      currentParagraph: paragraphIndex,
      currentSentence: 0,
      currentWord: 0,
      isPlaying: wasReading, // 如果之前在播放，继续播放新位置
    }))

    // 如果之前在播放，自动开始新位置的播放
    if (wasReading) {
      setTimeout(() => {
        readingService.start()
      }, 100) // 等待位置切换完成
    }
  }

  const handleSettingsChange = (newSettings: Partial<ReadingSettings>) => {
    setSettings(prev => ({
      ...prev,
      ...newSettings,
    }))
  }

  const handleTranslationLanguageChange = (language: string) => {
    setSettings(prev => ({
      ...prev,
      translationLanguage: language,
    }))
  }

  const handleAddBookmark = (title: string, note?: string) => {
    try {
      const newBookmark = bookService.addBookmark(book.id, {
        paragraphIndex: readingState.currentParagraph,
        title,
        note,
      })
      setBookmarks(prev => [...prev, newBookmark])
    } catch (error) {
      console.error('Failed to add bookmark:', error)
    }
  }

  const handleRemoveBookmark = (bookmarkId: string) => {
    try {
      bookService.removeBookmark(book.id, bookmarkId)
      setBookmarks(prev => prev.filter(bookmark => bookmark.id !== bookmarkId))
    } catch (error) {
      console.error('Failed to remove bookmark:', error)
    }
  }

  const handleJumpToBookmark = (paragraphIndex: number) => {
    handleProgressChange(paragraphIndex)
  }

  // 返回到当前朗读位置 - 简化版本，避免干扰朗读状态
  const handleBackToReading = () => {
    console.log('🎯 返回朗读位置被点击')

    const currentParagraph = readingState.currentParagraph
    const currentSentence = readingState.currentSentence
    console.log('📍 当前位置:', { currentParagraph, currentSentence })

    // 立即隐藏按钮
    setShowBackToReading(false)

    // 标记用户正在手动滚动
    userScrollingRef.current = true

    // 简单策略：直接使用ReadingService的当前位置信息
    const servicePosition = readingService.getCurrentPosition()
    console.log('📊 服务位置:', servicePosition)

    // 优先尝试定位到句子
    const sentenceElement = document.querySelector(
      `[data-paragraph="${servicePosition.paragraphIndex}"][data-sentence="${servicePosition.sentenceIndex}"]`
    )

    if (sentenceElement) {
      console.log('✅ 找到句子元素，直接定位')
      sentenceElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      })
    } else {
      // 尝试段落定位
      const paragraphElement = document.querySelector(
        `[data-paragraph-index="${servicePosition.paragraphIndex}"]`
      )

      if (paragraphElement) {
        console.log('✅ 找到段落元素，定位到段落')
        paragraphElement.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        })
      } else {
        console.log('⚠️ 使用虚拟化安全滚动')
        // 使用虚拟化组件的滚动方法，不干扰朗读状态
        const containerElement = document.querySelector('.prose')
        if (containerElement) {
          // 计算相对位置
          const totalParagraphs = paragraphs.length
          const scrollPercentage =
            servicePosition.paragraphIndex / totalParagraphs
          const containerHeight = containerElement.scrollHeight
          const targetPosition = containerHeight * scrollPercentage
          const viewportHeight = window.innerHeight

          window.scrollTo({
            top: Math.max(0, targetPosition - viewportHeight * 0.3),
            behavior: 'smooth',
          })
        }
      }
    }

    // 重置滚动标志
    setTimeout(() => {
      userScrollingRef.current = false
    }, 1000)
  }

  // 处理可见区域更新
  const handleVisibleRangeUpdate = useCallback(
    (range: { start: number; end: number }) => {
      defaultTranslationManager.updateVisibleRange(range)
    },
    []
  )

  // 重试翻译
  const handleRetryTranslation = useCallback(async (paragraphIndex: number) => {
    await defaultTranslationManager.retryTranslation(paragraphIndex)
  }, [])

  const handleVoiceChange = async (voice: TTSVoice) => {
    try {
      // 先完全停止当前朗读，避免同时播放两个声音
      const wasReading =
        readingService.getIsReading() && !readingService.getIsPaused()

      // 强制停止朗读
      readingService.forceStopKeepPosition()
      setReadingState(prev => ({
        ...prev,
        isPlaying: false,
      }))

      // 等待停止完成后设置新语音
      setTimeout(async () => {
        try {
          await readingService.setVoice(voice)
          setCurrentVoice(voice)

          // 保存语音选择到localStorage
          localStorage.setItem('preferredVoice', JSON.stringify(voice))

          // 如果之前在播放，用新语音继续
          if (wasReading) {
            // 再等待一点时间确保语音完全设置好
            setTimeout(() => {
              readingService.start()
              setReadingState(prev => ({
                ...prev,
                isPlaying: true,
              }))
            }, 200)
          }
        } catch (error) {
          console.error('Error in voice change timeout:', error)
        }
      }, 200) // 增加延迟确保停止完成
    } catch (error) {
      console.error('Failed to set voice:', error)
    }
  }

  return (
    <div
      data-theme={settings.theme}
      className={`min-h-screen transition-colors ${
        settings.theme === 'dark'
          ? 'bg-gray-900'
          : settings.theme === 'sepia'
            ? 'bg-amber-50'
            : 'bg-white'
      }`}
    >
      {/* 顶部工具栏 */}
      <div
        className={`sticky top-0 z-10 border-b backdrop-blur-sm ${
          settings.theme === 'dark'
            ? 'bg-gray-900/90 border-gray-700'
            : settings.theme === 'sepia'
              ? 'bg-amber-50/90 border-amber-200'
              : 'bg-white/90 border-gray-200'
        }`}
      >
        <div className="max-w-6xl mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={onBackToLibrary}
                className={`p-2 rounded-lg transition-colors ${
                  settings.theme === 'dark'
                    ? 'hover:bg-gray-800 text-gray-300'
                    : settings.theme === 'sepia'
                      ? 'hover:bg-amber-100 text-amber-800'
                      : 'hover:bg-gray-100 text-gray-600'
                }`}
              >
                <ArrowLeft className="h-5 w-5" />
              </button>
              <div>
                <h1
                  className={`text-lg font-semibold ${
                    settings.theme === 'dark'
                      ? 'text-white'
                      : settings.theme === 'sepia'
                        ? 'text-amber-900'
                        : 'text-gray-900'
                  }`}
                >
                  {book.title}
                </h1>
                <p
                  className={`text-sm ${
                    settings.theme === 'dark'
                      ? 'text-gray-400'
                      : settings.theme === 'sepia'
                        ? 'text-amber-700'
                        : 'text-gray-500'
                  }`}
                >
                  {paragraphs.length} 段落
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={() =>
                  setSettings(prev => ({
                    ...prev,
                    showBookmarks: !prev.showBookmarks,
                  }))
                }
                className={`p-2 rounded-lg transition-colors ${
                  settings.showBookmarks
                    ? settings.theme === 'dark'
                      ? 'bg-blue-600 text-white'
                      : settings.theme === 'sepia'
                        ? 'bg-orange-500 text-white'
                        : 'bg-blue-600 text-white'
                    : settings.theme === 'dark'
                      ? 'hover:bg-gray-800 text-gray-300'
                      : settings.theme === 'sepia'
                        ? 'hover:bg-amber-100 text-amber-800'
                        : 'hover:bg-gray-100 text-gray-600'
                }`}
                title="书签"
              >
                <Bookmark className="h-5 w-5" />
              </button>

              <button
                onClick={() =>
                  setSettings(prev => ({
                    ...prev,
                    showTranslation: !prev.showTranslation,
                  }))
                }
                className={`p-2 rounded-lg transition-colors ${
                  settings.showTranslation
                    ? settings.theme === 'dark'
                      ? 'bg-green-600 text-white'
                      : settings.theme === 'sepia'
                        ? 'bg-green-600 text-white'
                        : 'bg-green-600 text-white'
                    : settings.theme === 'dark'
                      ? 'hover:bg-gray-800 text-gray-300'
                      : settings.theme === 'sepia'
                        ? 'hover:bg-amber-100 text-amber-800'
                        : 'hover:bg-gray-100 text-gray-600'
                }`}
                title="翻译"
              >
                <Languages className="h-5 w-5" />
              </button>

              <button
                onClick={() => setShowSettings(!showSettings)}
                className={`p-2 rounded-lg transition-colors ${
                  settings.theme === 'dark'
                    ? 'hover:bg-gray-800 text-gray-300'
                    : settings.theme === 'sepia'
                      ? 'hover:bg-amber-100 text-amber-800'
                      : 'hover:bg-gray-100 text-gray-600'
                }`}
                title="设置"
              >
                <Settings className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-6xl mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* 阅读区域 */}
          <div
            className={`${
              settings.showTranslation || settings.showBookmarks
                ? 'lg:col-span-3'
                : 'lg:col-span-4'
            }`}
          >
            <OptimizedReadingArea
              paragraphs={paragraphs}
              settings={settings}
              readingState={readingState}
              onProgressChange={handleProgressChange}
              userScrollingRef={userScrollingRef}
              translations={translations}
              onRetryTranslation={handleRetryTranslation}
              onUpdateVisibleRange={handleVisibleRangeUpdate}
            />
          </div>

          {/* 侧边栏 */}
          {(settings.showTranslation || settings.showBookmarks) && (
            <div className="lg:col-span-1 space-y-6">
              {/* 书签面板 */}
              {settings.showBookmarks && (
                <BookmarkPanel
                  bookmarks={bookmarks}
                  currentParagraph={readingState.currentParagraph}
                  theme={settings.theme}
                  onAddBookmark={handleAddBookmark}
                  onRemoveBookmark={handleRemoveBookmark}
                  onJumpToBookmark={handleJumpToBookmark}
                />
              )}

              {/* 翻译面板 */}
              {settings.showTranslation && (
                <TranslationPanel
                  currentParagraph={paragraphs[readingState.currentParagraph]}
                  targetLanguage={settings.translationLanguage}
                  theme={settings.theme}
                  onLanguageChange={handleTranslationLanguageChange}
                />
              )}
            </div>
          )}
        </div>
      </div>

      {/* 播放控制栏 */}
      <PlayerControls
        readingState={readingState}
        totalParagraphs={paragraphs.length}
        onPlayPause={handlePlayPause}
        onStop={handleStop}
        onSpeedChange={handleSpeedChange}
        onVolumeChange={handleVolumeChange}
        onProgressChange={handleProgressChange}
        theme={settings.theme}
        voices={availableVoices}
        currentVoice={currentVoice}
        onVoiceChange={handleVoiceChange}
      />

      {/* 设置面板 */}
      {showSettings && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div
            className={`max-w-md w-full rounded-lg shadow-xl ${
              settings.theme === 'dark'
                ? 'bg-gray-800'
                : settings.theme === 'sepia'
                  ? 'bg-amber-50'
                  : 'bg-white'
            }`}
          >
            <div className="p-6">
              <h3
                className={`text-lg font-semibold mb-4 ${
                  settings.theme === 'dark'
                    ? 'text-white'
                    : settings.theme === 'sepia'
                      ? 'text-amber-900'
                      : 'text-gray-900'
                }`}
              >
                阅读设置
              </h3>

              {/* 设置选项将在后续实现 */}
              <div className="space-y-4">
                <div>
                  <label
                    className={`block text-sm font-medium mb-2 ${
                      settings.theme === 'dark'
                        ? 'text-gray-300'
                        : settings.theme === 'sepia'
                          ? 'text-amber-800'
                          : 'text-gray-700'
                    }`}
                  >
                    字体大小: {settings.fontSize}px
                  </label>
                  <input
                    type="range"
                    min="12"
                    max="24"
                    value={settings.fontSize}
                    onChange={e =>
                      handleSettingsChange({
                        fontSize: parseInt(e.target.value),
                      })
                    }
                    className="w-full"
                  />
                </div>

                <div>
                  <label
                    className={`block text-sm font-medium mb-2 ${
                      settings.theme === 'dark'
                        ? 'text-gray-300'
                        : settings.theme === 'sepia'
                          ? 'text-amber-800'
                          : 'text-gray-700'
                    }`}
                  >
                    主题
                  </label>
                  <select
                    value={settings.theme}
                    onChange={e =>
                      handleSettingsChange({
                        theme: e.target.value as 'light' | 'dark' | 'sepia',
                      })
                    }
                    className={`w-full p-2 border rounded-md ${
                      settings.theme === 'dark'
                        ? 'bg-gray-700 border-gray-600 text-white'
                        : settings.theme === 'sepia'
                          ? 'bg-amber-100 border-amber-300 text-amber-900'
                          : 'bg-white border-gray-300'
                    }`}
                  >
                    <option value="light">浅色</option>
                    <option value="dark">深色</option>
                    <option value="sepia">护眼</option>
                  </select>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="showTranslation"
                    checked={settings.showTranslation}
                    onChange={e =>
                      handleSettingsChange({
                        showTranslation: e.target.checked,
                      })
                    }
                    className="mr-2"
                  />
                  <label
                    htmlFor="showTranslation"
                    className={`text-sm ${
                      settings.theme === 'dark'
                        ? 'text-gray-300'
                        : settings.theme === 'sepia'
                          ? 'text-amber-800'
                          : 'text-gray-700'
                    }`}
                  >
                    显示翻译
                  </label>
                </div>

                {/* 翻译语言选择 */}
                {settings.showTranslation && (
                  <div>
                    <label
                      className={`block text-sm font-medium mb-2 ${
                        settings.theme === 'dark'
                          ? 'text-gray-300'
                          : settings.theme === 'sepia'
                            ? 'text-amber-800'
                            : 'text-gray-700'
                      }`}
                    >
                      翻译语言
                    </label>
                    <select
                      value={settings.translationLanguage}
                      onChange={e =>
                        handleSettingsChange({
                          translationLanguage: e.target.value,
                        })
                      }
                      className={`w-full p-2 border rounded-md ${
                        settings.theme === 'dark'
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : settings.theme === 'sepia'
                            ? 'bg-amber-100 border-amber-300 text-amber-900'
                            : 'bg-white border-gray-300'
                      }`}
                    >
                      <option value="zh">中文</option>
                      <option value="en">English</option>
                      <option value="ja">日本語</option>
                      <option value="ko">한국어</option>
                      <option value="fr">Français</option>
                      <option value="de">Deutsch</option>
                      <option value="es">Español</option>
                      <option value="ru">Русский</option>
                    </select>
                  </div>
                )}
              </div>

              {/* 快捷键说明 */}
              <div className="mt-6 pt-4 border-t border-opacity-20">
                <h4
                  className={`text-sm font-medium mb-3 ${
                    settings.theme === 'dark'
                      ? 'text-gray-300'
                      : settings.theme === 'sepia'
                        ? 'text-amber-800'
                        : 'text-gray-700'
                  }`}
                >
                  快捷键
                </h4>
                <div
                  className={`space-y-2 text-xs ${
                    settings.theme === 'dark'
                      ? 'text-gray-400'
                      : settings.theme === 'sepia'
                        ? 'text-amber-700'
                        : 'text-gray-600'
                  }`}
                >
                  <div className="flex justify-between">
                    <span>播放/暂停</span>
                    <kbd className="px-2 py-1 bg-gray-100 rounded text-gray-800">
                      空格
                    </kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>停止</span>
                    <kbd className="px-2 py-1 bg-gray-100 rounded text-gray-800">
                      Esc
                    </kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>上一段/下一段</span>
                    <kbd className="px-2 py-1 bg-gray-100 rounded text-gray-800">
                      Ctrl + ←/→
                    </kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>加速/减速</span>
                    <kbd className="px-2 py-1 bg-gray-100 rounded text-gray-800">
                      Ctrl + ↑/↓
                    </kbd>
                  </div>
                  <div className="flex justify-between">
                    <span>切换翻译</span>
                    <kbd className="px-2 py-1 bg-gray-100 rounded text-gray-800">
                      Ctrl + T
                    </kbd>
                  </div>
                </div>
              </div>

              <div className="flex justify-end mt-6">
                <button
                  onClick={() => setShowSettings(false)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  确定
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 返回朗读位置的浮动按钮 */}
      {showBackToReading && readingState.isPlaying && (
        <button
          onClick={handleBackToReading}
          className={`fixed bottom-24 right-6 z-50 px-3 py-2 rounded-full shadow-xl transition-all duration-300 transform hover:scale-110 animate-pulse ${
            settings.theme === 'dark'
              ? 'bg-blue-600 hover:bg-blue-700 text-white border-2 border-blue-400'
              : settings.theme === 'sepia'
                ? 'bg-amber-600 hover:bg-amber-700 text-white border-2 border-amber-400'
                : 'bg-blue-500 hover:bg-blue-600 text-white border-2 border-blue-300'
          }`}
          title="返回当前朗读位置"
        >
          <div className="flex items-center gap-1">
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 11l3-3m0 0l3 3m-3-3v8m0-13a9 9 0 110 18 9 9 0 010-18z"
              />
            </svg>
            <span className="text-xs font-medium">朗读位置</span>
          </div>
        </button>
      )}
    </div>
  )
}
