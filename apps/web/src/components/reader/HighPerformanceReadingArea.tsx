import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react'
import { ReadingSettings, ReadingState } from './BookReader'

interface HighPerformanceReadingAreaProps {
  paragraphs: string[]
  settings: ReadingSettings
  readingState: ReadingState
  onProgressChange: (paragraphIndex: number) => void
}

// 文本处理缓存
const textProcessingCache = new Map<
  string,
  {
    sentences: string[]
    words: string[][]
  }
>()

// 缓存文本处理结果
const getCachedTextProcessing = (paragraph: string) => {
  if (textProcessingCache.has(paragraph)) {
    return textProcessingCache.get(paragraph)!
  }

  const sentences = splitIntoSentences(paragraph)
  const words = sentences.map(sentence => splitIntoWords(sentence))

  const result = { sentences, words }
  textProcessingCache.set(paragraph, result)

  // 限制缓存大小
  if (textProcessingCache.size > 1000) {
    const firstKey = textProcessingCache.keys().next().value
    textProcessingCache.delete(firstKey)
  }

  return result
}

// 全局文本处理函数
const splitIntoSentences = (text: string): string[] => {
  // 改进的句子分割，避免过度分割，保持段落的自然流动
  const sentences = []
  const regex = /([^.!?。！？]*[.!?。！？]+(?:\s+|$))/g
  let match
  let lastIndex = 0

  while ((match = regex.exec(text)) !== null) {
    const sentence = match[1].trim()
    if (sentence.length > 0) {
      sentences.push(sentence)
    }
    lastIndex = regex.lastIndex
  }

  if (lastIndex < text.length) {
    const remaining = text.substring(lastIndex).trim()
    if (remaining) {
      sentences.push(remaining)
    }
  }

  // 如果没有找到明确的句子分割，将整个段落作为一个句子
  if (sentences.length === 0 && text.trim().length > 0) {
    sentences.push(text.trim())
  }

  return sentences.filter(s => s.length > 0)
}

const splitIntoWords = (text: string): string[] => {
  return text.split(/(\s+|[，。！？,.!?;:])/).filter(w => w.length > 0)
}

// 高性能段落组件
const ParagraphItem = React.memo<ParagraphItemProps>(
  ({ index, style, data }) => {
    const {
      paragraphs,
      settings,
      readingState,
      onProgressChange,
      measureHeight,
    } = data
    const elementRef = useRef<HTMLDivElement>(null)
    const paragraph = paragraphs[index]
    const isCurrentParagraph = index === readingState.currentParagraph

    // 测量高度并缓存 - 使用ResizeObserver获得更好的性能
    useEffect(() => {
      const element = elementRef.current
      if (!element) return

      const resizeObserver = new ResizeObserver(entries => {
        for (const entry of entries) {
          const height = entry.contentRect.height
          if (height > 0) {
            measureHeight(index, height)
          }
        }
      })

      resizeObserver.observe(element)
      return () => resizeObserver.disconnect()
    }, [index, measureHeight])

    // 使用缓存的文本处理结果
    const { sentences, words: sentenceWords } = useMemo(
      () => getCachedTextProcessing(paragraph),
      [paragraph]
    )

    return (
      <div
        ref={elementRef}
        style={style}
        className={`mb-6 p-4 rounded-lg transition-all duration-300 cursor-pointer ${
          settings.theme === 'dark'
            ? 'hover:bg-gray-800/50'
            : settings.theme === 'sepia'
              ? 'hover:bg-amber-100/30'
              : 'hover:bg-gray-50'
        }`}
        onClick={() => onProgressChange(index)}
      >
        {sentences.map((sentence, sentenceIndex) => {
          const isCurrentSentence =
            isCurrentParagraph && sentenceIndex === readingState.currentSentence
          const words = sentenceWords[sentenceIndex]

          return (
            <span
              key={sentenceIndex}
              className={`inline relative ${
                isCurrentSentence && readingState.isPlaying
                  ? 'sentence-highlight-inline'
                  : ''
              }`}
            >
              {words.map((word, wordIndex) => {
                const isCurrentWord =
                  isCurrentSentence &&
                  wordIndex === readingState.currentWord &&
                  readingState.isPlaying
                const isSpace = /^\s+$/.test(word)
                const isPunctuation = /^[，。！？,.!?;:]+$/.test(word)

                if (isSpace) {
                  return <span key={wordIndex}> </span>
                }

                const nextWord = words[wordIndex + 1]
                const needsSpace =
                  nextWord &&
                  !isPunctuation &&
                  !/^\s+$/.test(nextWord) &&
                  !/^[，。！？,.!?;:]+$/.test(nextWord)

                return (
                  <React.Fragment key={wordIndex}>
                    <span className="reading-text">{word}</span>
                    {needsSpace && <span> </span>}
                  </React.Fragment>
                )
              })}
            </span>
          )
        })}
      </div>
    )
  }
)

ParagraphItem.displayName = 'ParagraphItem'

export default function HighPerformanceReadingArea({
  paragraphs,
  settings,
  readingState,
  onProgressChange,
}: HighPerformanceReadingAreaProps) {
  const listRef = useRef<List>(null)
  const heightCache = useRef<Map<number, number>>(new Map())
  const [containerHeight, setContainerHeight] = useState(CONTAINER_HEIGHT)

  // 获取项目高度
  const getItemSize = useCallback((index: number) => {
    return heightCache.current.get(index) || ESTIMATED_PARAGRAPH_HEIGHT
  }, [])

  // 测量并缓存高度
  const measureHeight = useCallback((index: number, height: number) => {
    const currentHeight = heightCache.current.get(index)
    if (currentHeight !== height && height > 0) {
      heightCache.current.set(index, height)
      // 重新计算列表大小
      if (listRef.current) {
        listRef.current.resetAfterIndex(index, false)
      }
    }
  }, [])

  // 自动滚动到当前段落 - 位置偏上
  useEffect(() => {
    if (listRef.current && readingState.isPlaying) {
      // 使用自定义滚动，让当前段落显示在上方1/3处
      const list = listRef.current
      const itemHeight = 120 // 估算的段落高度
      const containerHeight = window.innerHeight - 200

      // 计算目标滚动位置：让当前段落显示在容器上方1/3处
      const targetOffset = containerHeight * 0.3
      const targetScrollTop =
        readingState.currentParagraph * itemHeight - targetOffset

      list.scrollTo(Math.max(0, targetScrollTop))
    }
  }, [readingState.currentParagraph, readingState.isPlaying])

  // 更新容器高度
  useEffect(() => {
    const updateContainerHeight = () => {
      setContainerHeight(window.innerHeight - 200) // 减去头部和控制栏的高度
    }

    updateContainerHeight()
    window.addEventListener('resize', updateContainerHeight)
    return () => window.removeEventListener('resize', updateContainerHeight)
  }, [])

  // 准备传递给子组件的数据
  const itemData = useMemo(
    () => ({
      paragraphs,
      settings,
      readingState,
      onProgressChange,
      measureHeight,
    }),
    [paragraphs, settings, readingState, onProgressChange, measureHeight]
  )

  return (
    <div
      className={`prose max-w-none ${
        settings.theme === 'dark'
          ? 'prose-invert'
          : settings.theme === 'sepia'
            ? 'prose-amber'
            : ''
      }`}
      style={{
        fontSize: `${settings.fontSize}px`,
        lineHeight: settings.lineHeight,
        fontFamily: settings.fontFamily,
      }}
    >
      <div
        className={`${
          settings.theme === 'dark'
            ? 'text-gray-100'
            : settings.theme === 'sepia'
              ? 'text-amber-900'
              : 'text-gray-900'
        }`}
      >
        <List
          ref={listRef}
          height={containerHeight}
          itemCount={paragraphs.length}
          itemSize={getItemSize}
          itemData={itemData}
          overscanCount={2} // 预渲染2个额外项目以提高滚动性能
          useIsScrolling // 启用滚动状态优化
        >
          {ParagraphItem}
        </List>
      </div>

      {/* 阅读进度指示器 */}
      <div
        className={`fixed bottom-20 right-4 p-3 rounded-lg shadow-lg ${
          settings.theme === 'dark'
            ? 'bg-gray-800 text-white'
            : settings.theme === 'sepia'
              ? 'bg-amber-100 text-amber-900'
              : 'bg-white text-gray-900'
        }`}
      >
        <div className="text-sm font-medium">
          {readingState.currentParagraph + 1} / {paragraphs.length}
        </div>
        <div className="text-xs opacity-75 mt-1">
          {Math.round(
            ((readingState.currentParagraph + 1) / paragraphs.length) * 100
          )}
          %
        </div>
      </div>
    </div>
  )
}
