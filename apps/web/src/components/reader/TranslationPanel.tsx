import { useState, useEffect } from 'react'
import { Languages, Loader2, AlertCircle } from 'lucide-react'
import { translateText } from '../../api/translation'

interface TranslationPanelProps {
  currentParagraph: string
  targetLanguage: string
  theme: 'light' | 'dark' | 'sepia'
  onLanguageChange: (language: string) => void
}

export default function TranslationPanel({
  currentParagraph,
  targetLanguage,
  theme,
  onLanguageChange,
}: TranslationPanelProps) {
  const [translation, setTranslation] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastTranslatedParagraph, setLastTranslatedParagraph] =
    useState<string>('')

  useEffect(() => {
    if (currentParagraph && currentParagraph !== lastTranslatedParagraph) {
      translateParagraph(currentParagraph)
    }
  }, [currentParagraph, targetLanguage])

  const translateParagraph = async (text: string) => {
    if (!text || text.trim().length === 0) {
      setTranslation('')
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      // 检测源语言（简单检测）
      const isEnglish = /^[a-zA-Z\s.,!?;:'"()-]+$/.test(text.trim())
      const fromLang = isEnglish ? 'en' : 'auto'

      const result = await translateText(
        text,
        fromLang,
        targetLanguage,
        'google'
      )
      setTranslation(result.translatedText)
      setLastTranslatedParagraph(text)
    } catch (err) {
      console.error('Translation error:', err)
      setError(err instanceof Error ? err.message : '翻译失败')
      setTranslation('')
    } finally {
      setIsLoading(false)
    }
  }

  const handleRetry = () => {
    if (currentParagraph) {
      translateParagraph(currentParagraph)
    }
  }

  return (
    <div
      className={`sticky top-20 rounded-lg shadow-sm border p-4 ${
        theme === 'dark'
          ? 'bg-gray-800 border-gray-700'
          : theme === 'sepia'
            ? 'bg-amber-100 border-amber-200'
            : 'bg-white border-gray-200'
      }`}
    >
      <div className="flex items-center mb-3">
        <Languages
          className={`h-5 w-5 mr-2 ${
            theme === 'dark'
              ? 'text-gray-400'
              : theme === 'sepia'
                ? 'text-amber-700'
                : 'text-gray-500'
          }`}
        />
        <h3
          className={`font-medium ${
            theme === 'dark'
              ? 'text-white'
              : theme === 'sepia'
                ? 'text-amber-900'
                : 'text-gray-900'
          }`}
        >
          段落翻译
        </h3>
      </div>

      <div
        className={`min-h-[120px] ${
          theme === 'dark'
            ? 'text-gray-300'
            : theme === 'sepia'
              ? 'text-amber-800'
              : 'text-gray-700'
        }`}
      >
        {isLoading ? (
          <div className="flex items-center justify-center h-24">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span className="text-sm">翻译中...</span>
          </div>
        ) : error ? (
          <div
            className={`p-3 rounded-md ${
              theme === 'dark'
                ? 'bg-red-900/30 border border-red-700'
                : theme === 'sepia'
                  ? 'bg-red-100 border border-red-300'
                  : 'bg-red-50 border border-red-200'
            }`}
          >
            <div className="flex items-start">
              <AlertCircle
                className={`h-4 w-4 mt-0.5 mr-2 flex-shrink-0 ${
                  theme === 'dark' ? 'text-red-400' : 'text-red-500'
                }`}
              />
              <div>
                <p
                  className={`text-sm ${
                    theme === 'dark' ? 'text-red-300' : 'text-red-700'
                  }`}
                >
                  {error}
                </p>
                <button
                  onClick={handleRetry}
                  className={`mt-2 text-xs underline ${
                    theme === 'dark'
                      ? 'text-red-400 hover:text-red-300'
                      : 'text-red-600 hover:text-red-500'
                  }`}
                >
                  重试
                </button>
              </div>
            </div>
          </div>
        ) : translation ? (
          <div className="space-y-3">
            <div
              className={`p-3 rounded-md text-sm leading-relaxed ${
                theme === 'dark'
                  ? 'bg-gray-700/50'
                  : theme === 'sepia'
                    ? 'bg-amber-50'
                    : 'bg-gray-50'
              }`}
            >
              {translation}
            </div>

            {/* 翻译信息 */}
            <div
              className={`text-xs opacity-75 ${
                theme === 'dark'
                  ? 'text-gray-500'
                  : theme === 'sepia'
                    ? 'text-amber-600'
                    : 'text-gray-400'
              }`}
            >
              由 Google 翻译提供
            </div>
          </div>
        ) : (
          <div
            className={`flex items-center justify-center h-24 text-sm opacity-75 ${
              theme === 'dark'
                ? 'text-gray-500'
                : theme === 'sepia'
                  ? 'text-amber-600'
                  : 'text-gray-400'
            }`}
          >
            选择段落查看翻译
          </div>
        )}
      </div>

      {/* 语言选择 */}
      <div className="mt-4 pt-3 border-t border-opacity-20">
        <label
          className={`block text-xs font-medium mb-2 ${
            theme === 'dark'
              ? 'text-gray-400'
              : theme === 'sepia'
                ? 'text-amber-700'
                : 'text-gray-500'
          }`}
        >
          翻译语言
        </label>
        <select
          value={targetLanguage}
          onChange={e => onLanguageChange(e.target.value)}
          className={`w-full text-sm p-2 border rounded-md ${
            theme === 'dark'
              ? 'bg-gray-700 border-gray-600 text-gray-300'
              : theme === 'sepia'
                ? 'bg-amber-50 border-amber-300 text-amber-900'
                : 'bg-white border-gray-300 text-gray-700'
          }`}
        >
          <option value="zh">中文</option>
          <option value="en">English</option>
          <option value="ja">日本語</option>
          <option value="ko">한국어</option>
          <option value="fr">Français</option>
          <option value="de">Deutsch</option>
          <option value="es">Español</option>
          <option value="ru">Русский</option>
        </select>
      </div>
    </div>
  )
}
