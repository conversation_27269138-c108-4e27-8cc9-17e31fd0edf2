# 电子书阅读器性能优化

## 问题描述

原始的电子书阅读器存在严重的性能问题：

1. **DOM节点过多**：为整本书的所有段落创建DOM元素，导致内存占用过高
2. **滚动性能差**：大量DOM元素导致滚动卡顿
3. **多重滚动条**：内部滚动容器与页面滚动冲突
4. **段落压缩bug**：虚拟滚动实现错误导致内容显示异常
5. **渲染抖动**：可见范围计算不稳定，导致频繁重新渲染和内容闪烁
6. **日志噪音**：开发环境中疯狂打印日志，影响调试体验

## 解决方案

### 1. 虚拟化渲染 (Windowing)

**实现方式**：

- 只渲染当前可见区域的段落（约15个段落）
- 使用占位符div维持正确的滚动高度
- 动态计算可见范围，根据滚动位置更新

**核心代码**：

```typescript
const updateVisibleRange = useCallback(() => {
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop
  const containerTop = container.offsetTop
  const containerHeight = window.innerHeight

  const buffer = 5 // 缓冲区大小
  const estimatedParagraphHeight = 120

  const startIndex = Math.max(
    0,
    Math.floor((scrollTop - containerTop) / estimatedParagraphHeight) - buffer
  )
  const endIndex = Math.min(
    paragraphs.length,
    startIndex + Math.ceil(containerHeight / estimatedParagraphHeight) + buffer * 2
  )

  setVisibleRange({ start: startIndex, end: endIndex })
}, [paragraphs.length])
```

### 2. 文本处理缓存

**问题**：每次渲染都重新分割句子和单词，计算开销大

**解决方案**：

- 全局缓存文本处理结果
- 使用LRU策略限制缓存大小（500个段落）
- 避免重复的正则表达式计算

**核心代码**：

```typescript
const textProcessingCache = new Map<
  string,
  {
    sentences: string[]
    words: string[][]
  }
>()

const getCachedTextProcessing = (paragraph: string) => {
  if (textProcessingCache.has(paragraph)) {
    return textProcessingCache.get(paragraph)!
  }

  const sentences = splitIntoSentences(paragraph)
  const words = sentences.map(sentence => splitIntoWords(sentence))

  const result = { sentences, words }
  textProcessingCache.set(paragraph, result)

  // 限制缓存大小
  if (textProcessingCache.size > 500) {
    const firstKey = textProcessingCache.keys().next().value
    textProcessingCache.delete(firstKey)
  }

  return result
}
```

### 3. 滚动性能优化

**问题**：频繁的滚动事件处理导致性能问题

**解决方案**：

- 使用`requestAnimationFrame`节流滚动事件
- 使用`passive: true`优化事件监听
- 同时监听resize事件以适应窗口变化

**核心代码**：

```typescript
useEffect(() => {
  let ticking = false

  const handleScroll = () => {
    if (!ticking) {
      requestAnimationFrame(() => {
        updateVisibleRange()
        ticking = false
      })
      ticking = true
    }
  }

  window.addEventListener('scroll', handleScroll, { passive: true })
  window.addEventListener('resize', handleScroll, { passive: true })

  return () => {
    window.removeEventListener('scroll', handleScroll)
    window.removeEventListener('resize', handleScroll)
  }
}, [updateVisibleRange])
```

### 4. 组件优化

**React.memo优化**：

- 段落组件使用`React.memo`避免不必要的重渲染
- 精确的依赖项控制，只在必要时更新

**占位符策略**：

- 顶部和底部使用固定高度的占位符div
- 维持正确的滚动条长度和位置
- 避免滚动跳跃

### 5. 单一滚动条设计

**问题**：原始设计有内部滚动容器，导致滚动条位置错误

**解决方案**：

- 移除内部滚动容器
- 使用页面级滚动
- 滚动条自然出现在页面右侧

## 性能提升

### 内存使用

- **优化前**：渲染所有段落（可能数千个DOM节点）
- **优化后**：只渲染15-25个段落（减少95%+的DOM节点）

### 滚动性能

- **优化前**：滚动卡顿，特别是大文件
- **优化后**：流畅的60fps滚动体验

### 渲染稳定性

- **优化前**：可见范围震荡，疯狂重新渲染，内容闪烁
- **优化后**：稳定的渲染，防抖处理，无闪烁

### 初始加载

- **优化前**：需要处理所有段落的文本分割
- **优化后**：只处理可见段落，按需加载

### CPU使用

- **优化前**：频繁的DOM操作和文本处理
- **优化后**：缓存复用，减少重复计算

## 使用方式

```typescript
import OptimizedReadingArea from './OptimizedReadingArea'

<OptimizedReadingArea
  paragraphs={paragraphs}
  settings={settings}
  readingState={readingState}
  onProgressChange={handleProgressChange}
/>
```

## 开发调试

在开发环境中，组件会在控制台输出当前渲染的段落范围：

```
Rendering paragraphs 0-15 of 1000
Rendering paragraphs 5-20 of 1000
```

## 注意事项

1. **估算高度**：使用固定的段落高度估算（120px），实际高度可能有差异
2. **缓冲区大小**：当前设置为5个段落，可根据需要调整
3. **缓存限制**：文本处理缓存限制为500个段落，适合大多数使用场景

## 未来优化方向

1. **动态高度测量**：实现更精确的段落高度计算
2. **预加载策略**：智能预加载用户可能访问的内容
3. **Web Workers**：将文本处理移到后台线程
4. **IndexedDB缓存**：持久化文本处理结果
