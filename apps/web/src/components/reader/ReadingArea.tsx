import React, { useEffect, useRef } from 'react'
import { ReadingSettings, ReadingState } from './BookReader'

interface ReadingAreaProps {
  paragraphs: string[]
  settings: ReadingSettings
  readingState: ReadingState
  onProgressChange: (paragraphIndex: number) => void
}

export default function ReadingArea({
  paragraphs,
  settings,
  readingState,
  onProgressChange,
}: ReadingAreaProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const currentParagraphRef = useRef<HTMLDivElement>(null)

  // 自动滚动到当前段落 - 位置偏上
  useEffect(() => {
    if (currentParagraphRef.current && readingState.isPlaying) {
      // 使用自定义滚动位置，让句子显示在屏幕上方1/3处
      const element = currentParagraphRef.current
      const elementRect = element.getBoundingClientRect()
      const viewportHeight = window.innerHeight

      // 计算目标位置：让元素顶部距离视口顶部约1/3的位置
      const targetOffset = viewportHeight * 0.3
      const currentScrollY = window.pageYOffset
      const elementTop = elementRect.top + currentScrollY
      const targetScrollY = elementTop - targetOffset

      window.scrollTo({
        top: Math.max(0, targetScrollY),
        behavior: 'smooth',
      })
    }
  }, [readingState.currentParagraph, readingState.isPlaying])

  const splitIntoSentences = (text: string): string[] => {
    // 改进的句子分割，避免过度分割，保持段落的自然流动
    // 只在明确的句子结束标点后分割，并且后面有空格或换行
    const sentences = []
    const regex = /([^.!?。！？]*[.!?。！？]+(?:\s+|$))/g
    let match
    let lastIndex = 0

    while ((match = regex.exec(text)) !== null) {
      const sentence = match[1].trim()
      if (sentence.length > 0) {
        sentences.push(sentence)
      }
      lastIndex = regex.lastIndex
    }

    // 添加剩余的文本（如果没有以标点结尾）
    if (lastIndex < text.length) {
      const remaining = text.substring(lastIndex).trim()
      if (remaining) {
        sentences.push(remaining)
      }
    }

    // 如果没有找到明确的句子分割，将整个段落作为一个句子
    if (sentences.length === 0 && text.trim().length > 0) {
      sentences.push(text.trim())
    }

    return sentences.filter(s => s.length > 0)
  }

  const splitIntoWords = (text: string): string[] => {
    // 改进的单词分割，支持中英文和标点符号，保留空格
    // 使用更精确的分割方式，确保单词间的空格被正确保留
    return text.split(/(\s+|[，。！？,.!?;:])/).filter(w => w.length > 0)
  }

  const renderParagraph = (paragraph: string, paragraphIndex: number) => {
    const isCurrentParagraph = paragraphIndex === readingState.currentParagraph
    const sentences = splitIntoSentences(paragraph)

    return (
      <div
        key={paragraphIndex}
        ref={isCurrentParagraph ? currentParagraphRef : null}
        className={`mb-6 p-4 rounded-lg transition-all duration-300 cursor-pointer ${
          settings.theme === 'dark'
            ? 'hover:bg-gray-800/50'
            : settings.theme === 'sepia'
              ? 'hover:bg-amber-100/30'
              : 'hover:bg-gray-50'
        }`}
        onClick={() => onProgressChange(paragraphIndex)}
      >
        {sentences.map((sentence, sentenceIndex) => {
          const isCurrentSentence =
            isCurrentParagraph && sentenceIndex === readingState.currentSentence
          const words = splitIntoWords(sentence)

          return (
            <span
              key={sentenceIndex}
              className={`inline relative ${
                isCurrentSentence && readingState.isPlaying
                  ? 'sentence-highlight-inline'
                  : ''
              }`}
            >
              {words.map((word, wordIndex) => {
                const isCurrentWord =
                  isCurrentSentence &&
                  wordIndex === readingState.currentWord &&
                  readingState.isPlaying
                const isSpace = /^\s+$/.test(word)
                const isPunctuation = /^[，。！？,.!?;:]+$/.test(word)

                if (isSpace) {
                  return <span key={wordIndex}> </span>
                }

                const nextWord = words[wordIndex + 1]
                const needsSpace =
                  nextWord &&
                  !isPunctuation &&
                  !/^\s+$/.test(nextWord) &&
                  !/^[，。！？,.!?;:]+$/.test(nextWord)

                return (
                  <React.Fragment key={wordIndex}>
                    <span className="reading-text">{word}</span>
                    {needsSpace && <span> </span>}
                  </React.Fragment>
                )
              })}
            </span>
          )
        })}
      </div>
    )
  }

  return (
    <div
      ref={containerRef}
      className={`prose max-w-none ${
        settings.theme === 'dark'
          ? 'prose-invert'
          : settings.theme === 'sepia'
            ? 'prose-amber'
            : ''
      }`}
      style={{
        fontSize: `${settings.fontSize}px`,
        lineHeight: settings.lineHeight,
        fontFamily: settings.fontFamily,
      }}
    >
      <div
        className={`${
          settings.theme === 'dark'
            ? 'text-gray-100'
            : settings.theme === 'sepia'
              ? 'text-amber-900'
              : 'text-gray-900'
        }`}
      >
        {paragraphs.map((paragraph, index) =>
          renderParagraph(paragraph, index)
        )}
      </div>

      {/* 阅读进度指示器 */}
      <div
        className={`fixed bottom-20 right-4 p-3 rounded-lg shadow-lg ${
          settings.theme === 'dark'
            ? 'bg-gray-800 text-white'
            : settings.theme === 'sepia'
              ? 'bg-amber-100 text-amber-900'
              : 'bg-white text-gray-900'
        }`}
      >
        <div className="text-sm font-medium">
          {readingState.currentParagraph + 1} / {paragraphs.length}
        </div>
        <div className="text-xs opacity-75 mt-1">
          {Math.round(
            ((readingState.currentParagraph + 1) / paragraphs.length) * 100
          )}
          %
        </div>
      </div>
    </div>
  )
}
