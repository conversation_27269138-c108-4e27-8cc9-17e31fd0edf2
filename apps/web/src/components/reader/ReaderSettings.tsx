import { ReadingSettings } from './BookReader'

interface ReaderSettingsProps {
  settings: ReadingSettings
  onSettingsChange: (settings: ReadingSettings) => void
  onClose: () => void
}

export default function ReaderSettings({
  settings,
  onSettingsChange,
  onClose,
}: ReaderSettingsProps) {
  const handleSettingChange = (key: keyof ReadingSettings, value: any) => {
    onSettingsChange({
      ...settings,
      [key]: value,
    })
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div
        className={`max-w-md w-full rounded-lg shadow-xl ${
          settings.theme === 'dark'
            ? 'bg-gray-800'
            : settings.theme === 'sepia'
              ? 'bg-amber-50'
              : 'bg-white'
        }`}
      >
        <div className="p-6">
          <h3
            className={`text-lg font-semibold mb-4 ${
              settings.theme === 'dark'
                ? 'text-white'
                : settings.theme === 'sepia'
                  ? 'text-amber-900'
                  : 'text-gray-900'
            }`}
          >
            阅读设置
          </h3>

          <div className="space-y-4">
            {/* 字体大小 */}
            <div>
              <label
                className={`block text-sm font-medium mb-2 ${
                  settings.theme === 'dark'
                    ? 'text-gray-300'
                    : settings.theme === 'sepia'
                      ? 'text-amber-800'
                      : 'text-gray-700'
                }`}
              >
                字体大小: {settings.fontSize}px
              </label>
              <input
                type="range"
                min="12"
                max="24"
                value={settings.fontSize}
                onChange={e => handleSettingChange('fontSize', parseInt(e.target.value))}
                className="w-full"
              />
            </div>

            {/* 行高 */}
            <div>
              <label
                className={`block text-sm font-medium mb-2 ${
                  settings.theme === 'dark'
                    ? 'text-gray-300'
                    : settings.theme === 'sepia'
                      ? 'text-amber-800'
                      : 'text-gray-700'
                }`}
              >
                行高: {settings.lineHeight}
              </label>
              <input
                type="range"
                min="1.2"
                max="2.0"
                step="0.1"
                value={settings.lineHeight}
                onChange={e => handleSettingChange('lineHeight', parseFloat(e.target.value))}
                className="w-full"
              />
            </div>

            {/* 字体 */}
            <div>
              <label
                className={`block text-sm font-medium mb-2 ${
                  settings.theme === 'dark'
                    ? 'text-gray-300'
                    : settings.theme === 'sepia'
                      ? 'text-amber-800'
                      : 'text-gray-700'
                }`}
              >
                字体
              </label>
              <select
                value={settings.fontFamily}
                onChange={e => handleSettingChange('fontFamily', e.target.value)}
                className={`w-full p-2 rounded border ${
                  settings.theme === 'dark'
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : settings.theme === 'sepia'
                      ? 'bg-amber-100 border-amber-300 text-amber-900'
                      : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                <option value="Inter">Inter</option>
                <option value="system-ui">系统字体</option>
                <option value="serif">衬线字体</option>
                <option value="monospace">等宽字体</option>
              </select>
            </div>

            {/* 主题 */}
            <div>
              <label
                className={`block text-sm font-medium mb-2 ${
                  settings.theme === 'dark'
                    ? 'text-gray-300'
                    : settings.theme === 'sepia'
                      ? 'text-amber-800'
                      : 'text-gray-700'
                }`}
              >
                主题
              </label>
              <div className="flex space-x-2">
                {[
                  { value: 'light', label: '浅色', bg: 'bg-white', border: 'border-gray-300' },
                  { value: 'dark', label: '深色', bg: 'bg-gray-800', border: 'border-gray-600' },
                  { value: 'sepia', label: '护眼', bg: 'bg-amber-50', border: 'border-amber-300' },
                ].map(theme => (
                  <button
                    key={theme.value}
                    onClick={() => handleSettingChange('theme', theme.value)}
                    className={`flex-1 p-2 rounded border text-xs ${
                      settings.theme === theme.value
                        ? 'ring-2 ring-blue-500'
                        : ''
                    } ${theme.bg} ${theme.border}`}
                  >
                    {theme.label}
                  </button>
                ))}
              </div>
            </div>

            {/* 翻译设置 */}
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={settings.showTranslation}
                  onChange={e => handleSettingChange('showTranslation', e.target.checked)}
                  className="mr-2"
                />
                <span
                  className={`text-sm ${
                    settings.theme === 'dark'
                      ? 'text-gray-300'
                      : settings.theme === 'sepia'
                        ? 'text-amber-800'
                        : 'text-gray-700'
                  }`}
                >
                  显示翻译
                </span>
              </label>
            </div>

            {settings.showTranslation && (
              <div>
                <label
                  className={`block text-sm font-medium mb-2 ${
                    settings.theme === 'dark'
                      ? 'text-gray-300'
                      : settings.theme === 'sepia'
                        ? 'text-amber-800'
                        : 'text-gray-700'
                  }`}
                >
                  翻译语言
                </label>
                <select
                  value={settings.translationLanguage}
                  onChange={e => handleSettingChange('translationLanguage', e.target.value)}
                  className={`w-full p-2 rounded border ${
                    settings.theme === 'dark'
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : settings.theme === 'sepia'
                        ? 'bg-amber-100 border-amber-300 text-amber-900'
                        : 'bg-white border-gray-300 text-gray-900'
                  }`}
                >
                  <option value="zh">中文</option>
                  <option value="en">English</option>
                  <option value="ja">日本語</option>
                  <option value="ko">한국어</option>
                </select>
              </div>
            )}
          </div>

          {/* 快捷键说明 */}
          <div className="mt-6 pt-4 border-t border-opacity-20">
            <h4
              className={`text-sm font-medium mb-3 ${
                settings.theme === 'dark'
                  ? 'text-gray-300'
                  : settings.theme === 'sepia'
                    ? 'text-amber-800'
                    : 'text-gray-700'
              }`}
            >
              快捷键
            </h4>
            <div
              className={`space-y-2 text-xs ${
                settings.theme === 'dark'
                  ? 'text-gray-400'
                  : settings.theme === 'sepia'
                    ? 'text-amber-700'
                    : 'text-gray-600'
              }`}
            >
              <div className="flex justify-between">
                <span>播放/暂停</span>
                <kbd className="px-2 py-1 bg-gray-100 rounded text-gray-800">
                  空格
                </kbd>
              </div>
              <div className="flex justify-between">
                <span>停止</span>
                <kbd className="px-2 py-1 bg-gray-100 rounded text-gray-800">
                  Esc
                </kbd>
              </div>
              <div className="flex justify-between">
                <span>上一段/下一段</span>
                <kbd className="px-2 py-1 bg-gray-100 rounded text-gray-800">
                  Ctrl + ←/→
                </kbd>
              </div>
              <div className="flex justify-between">
                <span>加速/减速</span>
                <kbd className="px-2 py-1 bg-gray-100 rounded text-gray-800">
                  Ctrl + ↑/↓
                </kbd>
              </div>
              <div className="flex justify-between">
                <span>切换翻译</span>
                <kbd className="px-2 py-1 bg-gray-100 rounded text-gray-800">
                  Ctrl + T
                </kbd>
              </div>
            </div>
          </div>

          <div className="flex justify-end mt-6">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              确定
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
