import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react'
import { ReadingSettings, ReadingState } from './BookReader'
import { ParagraphTranslation } from '../../services/translation/TranslationManager'
import { Loader2, AlertCircle, RefreshCw } from 'lucide-react'

interface OptimizedReadingAreaProps {
  paragraphs: string[]
  settings: ReadingSettings
  readingState: ReadingState
  onProgressChange: (paragraphIndex: number) => void
  userScrollingRef?: React.RefObject<boolean>
  translations?: Map<number, ParagraphTranslation>
  onRetryTranslation?: (paragraphIndex: number) => void
  onUpdateVisibleRange?: (range: { start: number; end: number }) => void
}

// 文本处理缓存
const textProcessingCache = new Map<
  string,
  {
    sentences: string[]
    words: string[][]
  }
>()

// 缓存文本处理结果
const getCachedTextProcessing = (paragraph: string) => {
  if (textProcessingCache.has(paragraph)) {
    return textProcessingCache.get(paragraph)!
  }

  const sentences = splitIntoSentences(paragraph)
  const words = sentences.map(sentence => splitIntoWords(sentence))

  const result = { sentences, words }
  textProcessingCache.set(paragraph, result)

  // 限制缓存大小
  if (textProcessingCache.size > 500) {
    const firstKey = textProcessingCache.keys().next().value
    textProcessingCache.delete(firstKey)
  }

  return result
}

// 全局文本处理函数
const splitIntoSentences = (text: string): string[] => {
  // 改进的句子分割，避免过度分割，保持段落的自然流动
  const sentences = []
  const regex = /([^.!?。！？]*[.!?。！？]+(?:\s+|$))/g
  let match
  let lastIndex = 0

  while ((match = regex.exec(text)) !== null) {
    const sentence = match[1].trim()
    if (sentence.length > 0) {
      sentences.push(sentence)
    }
    lastIndex = regex.lastIndex
  }

  if (lastIndex < text.length) {
    const remaining = text.substring(lastIndex).trim()
    if (remaining) {
      sentences.push(remaining)
    }
  }

  // 如果没有找到明确的句子分割，将整个段落作为一个句子
  if (sentences.length === 0 && text.trim().length > 0) {
    sentences.push(text.trim())
  }

  return sentences.filter(s => s.length > 0)
}

const splitIntoWords = (text: string): string[] => {
  return text.split(/(\s+|[，。！？,.!?;:])/).filter(w => w.length > 0)
}

// 高性能段落组件
const ParagraphItemComponent = React.memo<{
  paragraph: string
  index: number
  isCurrentParagraph: boolean
  settings: ReadingSettings
  readingState: ReadingState
  onProgressChange: (index: number) => void
  translation?: ParagraphTranslation
  onRetryTranslation?: (paragraphIndex: number) => void
}>(
  ({
    paragraph,
    index,
    isCurrentParagraph,
    settings,
    readingState,
    onProgressChange,
    translation,
    onRetryTranslation,
  }) => {
    const elementRef = useRef<HTMLDivElement>(null)

    // 使用缓存的文本处理结果
    const { sentences, words: sentenceWords } = useMemo(
      () => getCachedTextProcessing(paragraph),
      [paragraph]
    )

    return (
      <div
        ref={isCurrentParagraph ? elementRef : null}
        data-paragraph-index={index}
        className={`mb-6 transition-all duration-300 ${
          settings.showTranslation ? 'space-y-3' : ''
        }`}
      >
        {/* 原文段落 */}
        <div
          className={`p-4 rounded-lg cursor-pointer ${
            settings.theme === 'dark'
              ? 'hover:bg-gray-800/50'
              : settings.theme === 'sepia'
                ? 'hover:bg-amber-100/30'
                : 'hover:bg-gray-50'
          }`}
          onClick={() => onProgressChange(index)}
        >
          {sentences.map((sentence, sentenceIndex) => {
            const isCurrentSentence =
              isCurrentParagraph &&
              sentenceIndex === readingState.currentSentence
            const words = sentenceWords[sentenceIndex]

            return (
              <span
                key={sentenceIndex}
                className={`inline relative ${
                  isCurrentSentence && readingState.isPlaying
                    ? 'sentence-highlight-inline'
                    : ''
                }`}
                data-paragraph={index}
                data-sentence={sentenceIndex}
              >
                {words.map((word, wordIndex) => {
                  const isCurrentWord =
                    isCurrentSentence &&
                    wordIndex === readingState.currentWord &&
                    readingState.isPlaying
                  const isSpace = /^\s+$/.test(word)
                  const isPunctuation = /^[，。！？,.!?;:]+$/.test(word)

                  if (isSpace) {
                    return <span key={wordIndex}> </span>
                  }

                  const nextWord = words[wordIndex + 1]
                  const needsSpace =
                    nextWord &&
                    !isPunctuation &&
                    !/^\s+$/.test(nextWord) &&
                    !/^[，。！？,.!?;:]+$/.test(nextWord)

                  return (
                    <React.Fragment key={wordIndex}>
                      <span
                        className="inline relative reading-text"
                        data-paragraph={index}
                        data-sentence={sentenceIndex}
                        data-word={wordIndex}
                      >
                        {word}
                      </span>
                      {needsSpace && <span> </span>}
                    </React.Fragment>
                  )
                })}
              </span>
            )
          })}
        </div>

        {/* 翻译内容 */}
        {settings.showTranslation && (
          <div
            className={`px-4 py-3 rounded-lg border-l-4 ${
              settings.theme === 'dark'
                ? 'bg-gray-800/30 border-blue-500/50 text-gray-300'
                : settings.theme === 'sepia'
                  ? 'bg-amber-50/50 border-orange-400/50 text-amber-800'
                  : 'bg-blue-50/50 border-blue-400/50 text-gray-700'
            }`}
          >
            {translation?.isLoading ? (
              <div className="flex items-center space-x-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm opacity-75">翻译中...</span>
              </div>
            ) : translation?.error ? (
              <div className="flex items-start space-x-2">
                <AlertCircle className="h-4 w-4 mt-0.5 text-red-500 flex-shrink-0" />
                <div className="flex-1">
                  <p className="text-sm text-red-600 dark:text-red-400">
                    {translation.error}
                  </p>
                  {onRetryTranslation && (
                    <button
                      onClick={e => {
                        e.stopPropagation()
                        onRetryTranslation(index)
                      }}
                      className="mt-1 flex items-center space-x-1 text-xs text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                    >
                      <RefreshCw className="h-3 w-3" />
                      <span>重试</span>
                    </button>
                  )}
                </div>
              </div>
            ) : translation?.translatedText ? (
              <div className="space-y-2">
                <p className="text-sm leading-relaxed">
                  {translation.translatedText}
                </p>
                <div className="flex items-center justify-between text-xs opacity-60">
                  <span>翻译</span>
                  <span>{translation.targetLanguage.toUpperCase()}</span>
                </div>
              </div>
            ) : (
              <div className="text-sm opacity-60">等待翻译...</div>
            )}
          </div>
        )}
      </div>
    )
  }
)

ParagraphItemComponent.displayName = 'ParagraphItemComponent'

// 自定义比较函数，只在关键属性变化时重新渲染
const arePropsEqual = (prevProps: any, nextProps: any) => {
  // 基本属性比较
  if (
    prevProps.paragraph !== nextProps.paragraph ||
    prevProps.index !== nextProps.index ||
    prevProps.isCurrentParagraph !== nextProps.isCurrentParagraph
  ) {
    return false
  }

  // 设置比较
  if (
    prevProps.settings.theme !== nextProps.settings.theme ||
    prevProps.settings.fontSize !== nextProps.settings.fontSize ||
    prevProps.settings.lineHeight !== nextProps.settings.lineHeight ||
    prevProps.settings.fontFamily !== nextProps.settings.fontFamily ||
    prevProps.settings.showTranslation !== nextProps.settings.showTranslation
  ) {
    return false
  }

  // 翻译状态比较 - 更精确的比较
  const prevTranslation = prevProps.translation
  const nextTranslation = nextProps.translation

  if (prevTranslation !== nextTranslation) {
    if (!prevTranslation && !nextTranslation) {
      // 都为空，相等
    } else if (!prevTranslation || !nextTranslation) {
      // 一个为空一个不为空，不相等
      return false
    } else {
      // 都不为空，比较关键属性
      if (
        prevTranslation.translatedText !== nextTranslation.translatedText ||
        prevTranslation.isLoading !== nextTranslation.isLoading ||
        prevTranslation.error !== nextTranslation.error
      ) {
        return false
      }
    }
  }

  // 朗读状态比较 - 只有当前段落才需要比较
  if (prevProps.isCurrentParagraph) {
    if (
      prevProps.readingState.currentSentence !==
        nextProps.readingState.currentSentence ||
      prevProps.readingState.currentWord !==
        nextProps.readingState.currentWord ||
      prevProps.readingState.isPlaying !== nextProps.readingState.isPlaying
    ) {
      return false
    }
  }

  return true
}

// 使用自定义比较函数的优化组件
const ParagraphItem = React.memo(ParagraphItemComponent, arePropsEqual)

export default function OptimizedReadingArea({
  paragraphs,
  settings,
  readingState,
  onProgressChange,
  userScrollingRef,
  translations,
  onRetryTranslation,
  onUpdateVisibleRange,
}: OptimizedReadingAreaProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const currentParagraphRef = useRef<HTMLDivElement>(null)
  const sentenceHighlightRef = useRef<HTMLDivElement>(null)

  // 稳定的虚拟滚动状态
  const [visibleRange, setVisibleRange] = useState({
    start: 0,
    end: Math.min(20, paragraphs.length), // 初始渲染20个段落
  })

  // 段落高度估算 - 根据是否有翻译动态调整
  const estimatedParagraphHeight = useMemo(() => {
    return settings.showTranslation ? 200 : 120 // 有翻译时高度更大
  }, [settings.showTranslation])

  // 禁用浮动高亮，使用简单的单词级高亮
  const updateFloatingHighlights = useCallback(() => {
    // 隐藏浮动高亮，因为我们现在使用单词级别的CSS高亮
    if (sentenceHighlightRef.current) {
      sentenceHighlightRef.current.style.opacity = '0'
    }
  }, [])

  // 监听阅读状态变化，更新高亮位置
  useEffect(() => {
    updateFloatingHighlights()
  }, [readingState, updateFloatingHighlights])

  // 监听滚动和窗口大小变化，更新高亮位置
  useEffect(() => {
    const handleUpdate = () => {
      if (readingState.isPlaying) {
        updateFloatingHighlights()
      }
    }

    window.addEventListener('scroll', handleUpdate, { passive: true })
    window.addEventListener('resize', handleUpdate, { passive: true })

    return () => {
      window.removeEventListener('scroll', handleUpdate)
      window.removeEventListener('resize', handleUpdate)
    }
  }, [readingState.isPlaying, updateFloatingHighlights])

  // 确保当前段落在可见范围内
  useEffect(() => {
    const currentParagraph = readingState.currentParagraph
    setVisibleRange(prev => {
      // 如果当前段落不在可见范围内，扩展范围
      if (currentParagraph < prev.start || currentParagraph >= prev.end) {
        const buffer = 15
        const newStart = Math.max(0, currentParagraph - buffer)
        const newEnd = Math.min(paragraphs.length, currentParagraph + buffer)
        return { start: newStart, end: newEnd }
      }
      return prev
    })
  }, [readingState.currentParagraph, paragraphs.length])

  // 稳定的虚拟滚动逻辑
  useEffect(() => {
    const updateVisibleRange = () => {
      const viewportHeight = window.innerHeight
      const scrollTop = window.scrollY
      const viewportBottom = scrollTop + viewportHeight

      // 计算可见区域，使用较大的缓冲区以减少频繁更新
      const buffer = 10 // 增大缓冲区
      const visibleStart = Math.max(
        0,
        Math.floor(scrollTop / estimatedParagraphHeight) - buffer
      )
      const visibleEnd = Math.min(
        paragraphs.length,
        Math.ceil(viewportBottom / estimatedParagraphHeight) + buffer
      )

      // 确保当前朗读段落始终在范围内
      const currentParagraph = readingState.currentParagraph
      const adjustedStart = Math.min(visibleStart, currentParagraph)
      const adjustedEnd = Math.max(visibleEnd, currentParagraph + 1)

      // 只有当变化足够大时才更新，避免频繁重渲染
      setVisibleRange(prev => {
        const startDiff = Math.abs(prev.start - adjustedStart)
        const endDiff = Math.abs(prev.end - adjustedEnd)

        // 如果变化小于5个段落，不更新
        if (startDiff < 5 && endDiff < 5) {
          return prev
        }

        return { start: adjustedStart, end: adjustedEnd }
      })

      // 同时触发翻译更新
      if (onUpdateVisibleRange) {
        const translationStart = Math.max(0, adjustedStart - 3)
        const translationEnd = Math.min(paragraphs.length - 1, adjustedEnd + 3)
        onUpdateVisibleRange({ start: translationStart, end: translationEnd })
      }
    }

    // 防抖处理
    let timeoutId: NodeJS.Timeout
    const debouncedUpdate = () => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(updateVisibleRange, 300)
    }

    // 初始更新
    const initialTimeout = setTimeout(updateVisibleRange, 100)

    // 监听滚动事件
    window.addEventListener('scroll', debouncedUpdate, { passive: true })
    window.addEventListener('resize', debouncedUpdate, { passive: true })

    return () => {
      clearTimeout(timeoutId)
      clearTimeout(initialTimeout)
      window.removeEventListener('scroll', debouncedUpdate)
      window.removeEventListener('resize', debouncedUpdate)
    }
  }, [
    paragraphs.length,
    estimatedParagraphHeight,
    onUpdateVisibleRange,
    readingState.currentParagraph,
  ])

  // 性能监控 (开发环境) - 简化版本
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const timer = setTimeout(() => {
        console.log(`✅ Stable render: ${paragraphs.length} paragraphs`)
      }, 500)
      return () => clearTimeout(timer)
    }
  }, [paragraphs.length])

  // 自动滚动到当前段落 - 优化版本，减少闪烁
  useEffect(() => {
    // 如果用户正在手动滚动，跳过自动滚动
    if (userScrollingRef?.current) {
      return
    }

    // 只在开始播放时或段落变化时滚动
    if (readingState.isPlaying && currentParagraphRef.current) {
      // 延迟执行，避免与其他滚动事件冲突
      const scrollTimeout = setTimeout(() => {
        if (!currentParagraphRef.current || userScrollingRef?.current) return

        const element = currentParagraphRef.current
        const elementRect = element.getBoundingClientRect()
        const viewportHeight = window.innerHeight

        // 检查当前段落是否已经在合适的位置
        const isInGoodPosition =
          elementRect.top >= viewportHeight * 0.15 &&
          elementRect.top <= viewportHeight * 0.5

        // 只有当段落不在合适位置时才自动滚动
        if (!isInGoodPosition) {
          const targetOffset = viewportHeight * 0.25
          const currentScrollY = window.pageYOffset
          const elementTop = elementRect.top + currentScrollY
          const targetScrollY = elementTop - targetOffset

          window.scrollTo({
            top: Math.max(0, targetScrollY),
            behavior: 'smooth',
          })
        }
      }, 200)

      return () => clearTimeout(scrollTimeout)
    }
  }, [readingState.currentParagraph, readingState.isPlaying])

  // 优化的虚拟滚动渲染
  const virtualizedContent = useMemo(() => {
    const items = []

    // 顶部占位符
    if (visibleRange.start > 0) {
      const topSpacerHeight = visibleRange.start * estimatedParagraphHeight
      items.push(
        <div
          key="top-spacer"
          style={{ height: topSpacerHeight }}
          className="flex-shrink-0"
        />
      )
    }

    // 渲染可见段落
    for (
      let i = visibleRange.start;
      i < visibleRange.end && i < paragraphs.length;
      i++
    ) {
      const isCurrentParagraph = i === readingState.currentParagraph
      items.push(
        <div
          key={`paragraph-${i}`}
          ref={isCurrentParagraph ? currentParagraphRef : null}
          data-paragraph-index={i}
        >
          <ParagraphItem
            paragraph={paragraphs[i]}
            index={i}
            isCurrentParagraph={isCurrentParagraph}
            settings={settings}
            readingState={readingState}
            onProgressChange={onProgressChange}
            translation={translations?.get(i)}
            onRetryTranslation={onRetryTranslation}
          />
        </div>
      )
    }

    // 底部占位符
    if (visibleRange.end < paragraphs.length) {
      const remainingItems = paragraphs.length - visibleRange.end
      const bottomSpacerHeight = remainingItems * estimatedParagraphHeight
      items.push(
        <div
          key="bottom-spacer"
          style={{ height: bottomSpacerHeight }}
          className="flex-shrink-0"
        />
      )
    }

    return items
  }, [
    visibleRange,
    paragraphs,
    estimatedParagraphHeight,
    readingState.currentParagraph,
    readingState.currentSentence,
    readingState.currentWord,
    readingState.isPlaying,
    settings.theme,
    settings.fontSize,
    settings.lineHeight,
    settings.fontFamily,
    settings.showTranslation,
    onProgressChange,
    translations,
    onRetryTranslation,
  ])

  return (
    <div
      ref={containerRef}
      className={`prose max-w-none relative ${
        settings.theme === 'dark'
          ? 'prose-invert'
          : settings.theme === 'sepia'
            ? 'prose-amber'
            : ''
      }`}
      style={{
        fontSize: `${settings.fontSize}px`,
        lineHeight: settings.lineHeight,
        fontFamily: settings.fontFamily,
      }}
    >
      <div
        className={`${
          settings.theme === 'dark'
            ? 'text-gray-100'
            : settings.theme === 'sepia'
              ? 'text-amber-900'
              : 'text-gray-900'
        }`}
      >
        {virtualizedContent}
      </div>

      {/* 浮动高亮层 - 相对于容器定位 */}
      <div className="absolute inset-0 pointer-events-none z-10 overflow-hidden">
        {/* 句子高亮 - 简单的浮动高亮 */}
        <div
          ref={sentenceHighlightRef}
          className="absolute floating-sentence-highlight"
          style={{
            opacity: 0,
            backgroundColor: 'var(--highlight-bg)',
            color: 'var(--highlight-text)',
            padding: '2px 4px',
            borderRadius: '3px',
            transition: 'all 0.2s ease-out',
          }}
        />
      </div>

      {/* 阅读进度指示器 */}
      <div
        className={`fixed bottom-20 right-4 p-3 rounded-lg shadow-lg z-50 ${
          settings.theme === 'dark'
            ? 'bg-gray-800 text-white'
            : settings.theme === 'sepia'
              ? 'bg-amber-100 text-amber-900'
              : 'bg-white text-gray-900'
        }`}
      >
        <div className="text-sm font-medium">
          {readingState.currentParagraph + 1} / {paragraphs.length}
        </div>
        <div className="text-xs opacity-75 mt-1">
          {Math.round(
            ((readingState.currentParagraph + 1) / paragraphs.length) * 100
          )}
          %
        </div>
      </div>
    </div>
  )
}
