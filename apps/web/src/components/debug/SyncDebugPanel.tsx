import React, { useState, useEffect } from 'react'
import { perfectSyncTTS } from '../../services/speech/PerfectSyncTTSService'
import type { SyncCalibrationData } from '../../services/speech/PerfectSyncTTSService'

interface SyncDebugPanelProps {
  isOpen: boolean
  onClose: () => void
}

export const SyncDebugPanel: React.FC<SyncDebugPanelProps> = ({ isOpen, onClose }) => {
  const [calibrationData, setCalibrationData] = useState<SyncCalibrationData | null>(null)
  const [isTestingActive, setIsTestingActive] = useState(false)
  const [syncPrecision, setSyncPrecision] = useState(5) // 5ms精度
  const [latencyCompensation, setLatencyCompensation] = useState(30)
  const [enablePredictive, setEnablePredictive] = useState(true)
  const [enableAdaptive, setEnableAdaptive] = useState(true)
  const [syncDrift, setSyncDrift] = useState(0)
  const [testText, setTestText] = useState("The quick brown fox jumps over the lazy dog. This is a perfect synchronization test.")

  useEffect(() => {
    if (isOpen) {
      updateCalibrationData()
      
      // 监听校准更新事件
      const handleEvent = (event: any) => {
        if (event.type === 'calibration-update' && event.data?.calibration) {
          setCalibrationData(event.data.calibration)
        } else if (event.type === 'sync-drift-detected' && event.data?.driftMs) {
          setSyncDrift(event.data.driftMs)
        }
      }
      
      perfectSyncTTS.addEventListener(handleEvent)
      return () => perfectSyncTTS.removeEventListener(handleEvent)
    }
  }, [isOpen])

  const updateCalibrationData = () => {
    const data = perfectSyncTTS.getCalibrationData()
    setCalibrationData(data)
  }

  const applySettings = () => {
    perfectSyncTTS.updateOptions({
      syncPrecisionMs: syncPrecision,
      audioLatencyCompensation: latencyCompensation,
      enablePredictiveSync: enablePredictive,
      enableAdaptiveCalibration: enableAdaptive
    })
    updateCalibrationData()
  }

  const resetCalibration = () => {
    perfectSyncTTS.setLatencyCompensation(30) // 重置为默认值
    setLatencyCompensation(30)
    setSyncPrecision(5)
    setEnablePredictive(true)
    setEnableAdaptive(true)
    updateCalibrationData()
  }

  const runTest = async () => {
    setIsTestingActive(true)
    try {
      // 加载并播放测试文本
      await perfectSyncTTS.loadAndPrepare(testText)
      await perfectSyncTTS.play()
      
      // 5秒后停止
      setTimeout(() => {
        perfectSyncTTS.stop()
        setIsTestingActive(false)
      }, 5000)
    } catch (error) {
      console.error('Test failed:', error)
      setIsTestingActive(false)
    }
  }

  const getSyncQualityColor = (accuracy: number) => {
    if (accuracy > 95) return 'text-green-600'
    if (accuracy > 85) return 'text-blue-600'
    if (accuracy > 70) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getSyncQualityBadge = (quality: string) => {
    const colors = {
      excellent: 'bg-green-100 text-green-800',
      good: 'bg-blue-100 text-blue-800',
      fair: 'bg-yellow-100 text-yellow-800',
      poor: 'bg-red-100 text-red-800'
    }
    return colors[quality as keyof typeof colors] || colors.poor
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto m-4">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900">TTS同步调试面板</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl"
            >
              ×
            </button>
          </div>

          {/* 当前同步状态 */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="text-lg font-semibold mb-3">当前同步状态</h3>
            {calibrationData && (
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span className="text-sm text-gray-600">测量延迟:</span>
                  <span className="ml-2 font-mono">{calibrationData.measuredLatency.toFixed(0)}ms</span>
                </div>
                <div>
                  <span className="text-sm text-gray-600">系统延迟:</span>
                  <span className="ml-2 font-mono">{calibrationData.systemLatency.toFixed(2)}ms</span>
                </div>
                <div>
                  <span className="text-sm text-gray-600">音频缓冲延迟:</span>
                  <span className="ml-2 font-mono">{calibrationData.audioBufferLatency.toFixed(2)}ms</span>
                </div>
                <div>
                  <span className="text-sm text-gray-600">置信度:</span>
                  <span className="ml-2 font-mono">{(calibrationData.confidence * 100).toFixed(0)}%</span>
                </div>
              </div>
            )}
            {syncDrift !== 0 && (
              <div className="mt-2 p-2 bg-yellow-50 rounded">
                <span className="text-sm text-yellow-800">检测到同步偏移: {syncDrift.toFixed(0)}ms</span>
              </div>
            )}
          </div>

          {/* 测试文本配置 */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3">测试文本</h3>
            <textarea
              value={testText}
              onChange={(e) => setTestText(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg resize-none"
              rows={3}
              placeholder="输入用于测试的文本..."
            />
          </div>

          {/* 控制按钮 */}
          <div className="mb-6 flex flex-wrap gap-3">
            <button
              onClick={runTest}
              disabled={isTestingActive}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              {isTestingActive ? '测试中...' : '运行测试'}
            </button>
            <button
              onClick={applySettings}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
            >
              应用设置
            </button>
            <button
              onClick={resetCalibration}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
            >
              重置校准
            </button>
          </div>

          {/* 高级设置 */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="text-lg font-semibold mb-3">高级设置</h3>
            
            <div className="space-y-4">
              {/* 延迟补偿 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  延迟补偿: {latencyCompensation}ms
                </label>
                <input
                  type="range"
                  min="-200"
                  max="200"
                  step="10"
                  value={latencyCompensation}
                  onChange={(e) => setLatencyCompensation(Number(e.target.value))}
                  className="w-full"
                />
                <div className="text-xs text-gray-500 mt-1">
                  负值表示提前高亮，正值表示延迟高亮
                </div>
              </div>
              
              {/* 同步精度 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  同步精度: {syncPrecision}ms
                </label>
                <input
                  type="range"
                  min="1"
                  max="50"
                  step="1"
                  value={syncPrecision}
                  onChange={(e) => setSyncPrecision(Number(e.target.value))}
                  className="w-full"
                />
                <div className="text-xs text-gray-500 mt-1">
                  更低的值提供更高精度，但会消耗更多资源
                </div>
              </div>
              
              {/* 开关选项 */}
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={enablePredictive}
                    onChange={(e) => setEnablePredictive(e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700">启用预测性同步</span>
                </label>
                
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={enableAdaptive}
                    onChange={(e) => setEnableAdaptive(e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700">启用自适应校准</span>
                </label>
              </div>
            </div>
          </div>


          {/* 使用说明 */}
          <div className="text-sm text-gray-600">
            <h4 className="font-semibold mb-2">使用说明:</h4>
            <ul className="list-disc list-inside space-y-1">
              <li><strong>快速测试:</strong> 使用当前设置测试同步效果</li>
              <li><strong>自动校准:</strong> 自动寻找最佳偏移量参数</li>
              <li><strong>实时监控:</strong> 在朗读过程中实时显示同步状态</li>
              <li><strong>手动调整:</strong> 根据个人喜好微调偏移量</li>
              <li><strong>同步准确度 {'>'} 95%:</strong> 完美同步</li>
              <li><strong>同步准确度 85-95%:</strong> 良好同步</li>
              <li><strong>同步准确度 {'<'} 85%:</strong> 需要调整</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
