import { useState, useEffect, useCallback } from 'react'
import { BookData } from '../pages/ReaderPage'
import { ReadingSettings, ReadingState } from '../components/reader/BookReader'
import {
  readingService,
  ReadingProgress,
} from '../services/speech/ReadingService'
import { bookService, BookmarkData } from '../services/BookService'
import { TTSVoice } from '../services/speech/EnhancedTTSService'

export function useReaderState(book: BookData) {
  const [settings, setSettings] = useState<ReadingSettings>({
    fontSize: 16,
    lineHeight: 1.6,
    fontFamily: 'Inter',
    theme: 'light',
    showTranslation: false,
    translationLanguage: 'zh',
    showBookmarks: false,
    showVoiceSelector: true,
  })

  const [readingState, setReadingState] = useState<ReadingState>({
    currentParagraph: 0,
    currentSentence: 0,
    currentWord: 0,
    isPlaying: false,
    playbackRate: 1.0,
    volume: 1.0,
    progress: null,
  })

  const [showSettings, setShowSettings] = useState(false)
  const [bookmarks, setBookmarks] = useState<BookmarkData[]>([])
  const [availableVoices, setAvailableVoices] = useState<TTSVoice[]>([])
  const [currentVoice, setCurrentVoice] = useState<TTSVoice | null>(null)

  // 初始化阅读服务
  useEffect(() => {
    readingService.initialize(book.content, book.id)

    // 设置播放状态和音量
    readingService.setPlaybackRate(readingState.playbackRate)
    readingService.setVolume(readingState.volume)

    // 加载可用语音
    loadAvailableVoices()
  }, [book.content, book.id])

  // 加载可用语音
  const loadAvailableVoices = async () => {
    try {
      const voices = await readingService.getAvailableVoices()
      setAvailableVoices(voices)
      
      // 设置默认语音（优先选择中文语音）
      const chineseVoice = voices.find(v => 
        v.language.startsWith('zh') && v.provider === 'edge-tts'
      )
      const defaultVoice = chineseVoice || voices[0]
      if (defaultVoice) {
        setCurrentVoice(defaultVoice)
        readingService.setVoice(defaultVoice)
      }
    } catch (error) {
      console.error('Failed to load voices:', error)
    }
  }

  // 加载书签
  useEffect(() => {
    const loadBookmarks = async () => {
      try {
        const bookmarkList = await bookService.getBookmarks(book.id)
        setBookmarks(bookmarkList)
      } catch (error) {
        console.error('Failed to load bookmarks:', error)
      }
    }
    loadBookmarks()
  }, [book.id])

  // 监听阅读进度
  useEffect(() => {
    const handleProgress = (progress: ReadingProgress) => {
      setReadingState(prev => ({
        ...prev,
        currentParagraph: progress.currentParagraph,
        currentSentence: progress.currentSentence,
        currentWord: progress.currentWord,
        progress,
      }))
    }

    const handlePlayStateChange = (isPlaying: boolean) => {
      setReadingState(prev => ({ ...prev, isPlaying }))
    }

    readingService.on('progress', handleProgress)
    readingService.on('playStateChange', handlePlayStateChange)

    return () => {
      readingService.off('progress', handleProgress)
      readingService.off('playStateChange', handlePlayStateChange)
    }
  }, [])

  // 播放控制
  const handlePlay = useCallback(() => {
    readingService.play()
  }, [])

  const handlePause = useCallback(() => {
    readingService.pause()
  }, [])

  const handleStop = useCallback(() => {
    readingService.stop()
  }, [])

  const handleNext = useCallback(() => {
    readingService.nextParagraph()
  }, [])

  const handlePrevious = useCallback(() => {
    readingService.previousParagraph()
  }, [])

  const handleProgressChange = useCallback((paragraphIndex: number) => {
    readingService.seekToParagraph(paragraphIndex)
  }, [])

  const handlePlaybackRateChange = useCallback((rate: number) => {
    setReadingState(prev => ({ ...prev, playbackRate: rate }))
    readingService.setPlaybackRate(rate)
  }, [])

  const handleVolumeChange = useCallback((volume: number) => {
    setReadingState(prev => ({ ...prev, volume }))
    readingService.setVolume(volume)
  }, [])

  const handleVoiceChange = useCallback((voice: TTSVoice) => {
    setCurrentVoice(voice)
    readingService.setVoice(voice)
  }, [])

  // 书签管理
  const handleAddBookmark = useCallback(async (title: string, note?: string) => {
    try {
      const bookmark = await bookService.addBookmark(
        book.id,
        readingState.currentParagraph,
        title,
        note
      )
      setBookmarks(prev => [...prev, bookmark])
    } catch (error) {
      console.error('Failed to add bookmark:', error)
    }
  }, [book.id, readingState.currentParagraph])

  const handleDeleteBookmark = useCallback(async (bookmarkId: string) => {
    try {
      await bookService.deleteBookmark(bookmarkId)
      setBookmarks(prev => prev.filter(b => b.id !== bookmarkId))
    } catch (error) {
      console.error('Failed to delete bookmark:', error)
    }
  }, [])

  const handleGoToBookmark = useCallback((paragraphIndex: number) => {
    readingService.seekToParagraph(paragraphIndex)
  }, [])

  // 键盘快捷键
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return
      }

      switch (event.code) {
        case 'Space':
          event.preventDefault()
          if (readingState.isPlaying) {
            handlePause()
          } else {
            handlePlay()
          }
          break
        case 'Escape':
          event.preventDefault()
          handleStop()
          break
        case 'ArrowLeft':
          if (event.ctrlKey) {
            event.preventDefault()
            handlePrevious()
          }
          break
        case 'ArrowRight':
          if (event.ctrlKey) {
            event.preventDefault()
            handleNext()
          }
          break
        case 'ArrowUp':
          if (event.ctrlKey) {
            event.preventDefault()
            const newRate = Math.min(2.0, readingState.playbackRate + 0.1)
            handlePlaybackRateChange(newRate)
          }
          break
        case 'ArrowDown':
          if (event.ctrlKey) {
            event.preventDefault()
            const newRate = Math.max(0.5, readingState.playbackRate - 0.1)
            handlePlaybackRateChange(newRate)
          }
          break
        case 'KeyT':
          if (event.ctrlKey) {
            event.preventDefault()
            setSettings(prev => ({ ...prev, showTranslation: !prev.showTranslation }))
          }
          break
      }
    }

    window.addEventListener('keydown', handleKeyPress)
    return () => window.removeEventListener('keydown', handleKeyPress)
  }, [readingState.isPlaying, readingState.playbackRate, handlePlay, handlePause, handleStop, handleNext, handlePrevious, handlePlaybackRateChange])

  return {
    // State
    settings,
    readingState,
    showSettings,
    bookmarks,
    availableVoices,
    currentVoice,

    // State setters
    setSettings,
    setShowSettings,

    // Handlers
    handlePlay,
    handlePause,
    handleStop,
    handleNext,
    handlePrevious,
    handleProgressChange,
    handlePlaybackRateChange,
    handleVolumeChange,
    handleVoiceChange,
    handleAddBookmark,
    handleDeleteBookmark,
    handleGoToBookmark,
  }
}
