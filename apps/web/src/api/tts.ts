import axios from 'axios'

export interface VoiceInfo {
  id: string
  name: string
  language: string
  gender: string
  sample_rate?: number
  provider?: string
}

export interface TTSRequest {
  text: string
  voice?: string
  speed?: number
  pitch?: number
  output_format?: string
}

export interface TTSTimingItem {
  time_ms: number
  text_offset?: number
  text_length?: number
  word?: string
}

export interface TTSWithTimingsResponse {
  audio_base64: string
  mime: string
  timings: TTSTimingItem[]
}

const API_BASE =
  (import.meta as any).env?.VITE_API_BASE_URL || 'http://localhost:8000/api/v1'

export async function fetchVoices(): Promise<VoiceInfo[]> {
  const url = `${API_BASE}/tts/voices`
  const res = await axios.get(url)
  // expected: { voices: [...] }
  return res.data.voices || []
}

export async function synthesizeStream(payload: TTSRequest): Promise<Blob> {
  const url = `${API_BASE}/tts/synthesize-stream`
  const res = await axios.post(url, payload, { responseType: 'blob' })
  return res.data as Blob
}

export async function synthesizeWithTimings(
  payload: TTSRequest
): Promise<TTSWithTimingsResponse> {
  const url = `${API_BASE}/tts/synthesize-with-timings`
  const res = await axios.post(url, payload)
  return res.data as TTSWithTimingsResponse
}
