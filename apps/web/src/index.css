@import 'tailwindcss';

@theme {
  --font-sans: Inter, system-ui, sans-serif;

  /* Primary colors */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;
  --color-primary-950: #172554;
}

/* 自定义样式 */

/* 高亮动画 - 无跳动浮动效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes wordHighlight {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 阅读器高亮优化 - 防止布局重排 */
.reading-highlight-container {
  /* 使用GPU加速 */
  will-change: transform, opacity;
  /* 创建新的层叠上下文 */
  transform: translateZ(0);
}

.reading-word-highlight {
  /* 确保高亮不影响布局 */
  will-change: opacity, transform;
  backface-visibility: hidden;
  /* 使用GPU加速 */
  transform: translateZ(0);
  /* 优化渐变渲染 */
  background-clip: padding-box;
  /* 响应式圆角 */
  border-radius: 0.375rem; /* 6px */
  /* 确保在不同字体大小下都好看 */
  min-height: 1.2em;
  /* 优化边缘渲染 */
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}

.reading-sentence-highlight {
  /* 句子高亮优化 */
  will-change: opacity;
  backface-visibility: hidden;
  transform: translateZ(0);
}

/* 优化文本渲染 - 确保高亮时文字清晰 */
.reading-text {
  /* 优化文本渲染质量 */
  text-rendering: optimizeLegibility;
  /* 防止字体回退导致的闪烁 */
  font-display: swap;
  /* 确保文字清晰度 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* 优化文本选择 */
  user-select: none;
  /* 确保文本基线稳定 */
  vertical-align: baseline;
  /* 确保文字不受背景影响 */
  position: relative;
  z-index: 1;
}

/* 改进的句子高亮样式 - 清晰且美观 */
.floating-sentence-highlight {
  will-change: opacity;
  border-radius: 6px;
  /* 确保文字清晰度 */
  backface-visibility: visible;
  transform: none;
  mix-blend-mode: normal;
  isolation: auto;
  /* 添加微妙的过渡效果 */
  transition: all 0.2s ease-out;
}

/* 句子高亮动画 */
@keyframes sentenceHighlight {
  0% {
    opacity: 0;
    transform: translateX(-2px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 应用于句子高亮的动画类 */
.sentence-highlight-enter {
  animation: sentenceHighlight 0.3s ease-out;
}

/* 连续稳定的句子高亮样式 */
.sentence-highlight-inline {
  /* 使用简单的背景色 */
  background-color: var(--highlight-bg);
  color: var(--highlight-text);

  /* 关键：确保跨行时背景连续且不断开 */
  box-decoration-break: clone;
  -webkit-box-decoration-break: clone;

  /* 使用极小的padding确保连续性，但不影响布局 */
  padding: 2px 0;

  /* 完全禁用所有动画和过渡效果，避免晃动 */
  transition: none !important;
  animation: none !important;

  /* 确保不影响文本流和布局 */
  display: inline;
  line-height: inherit;
  font-size: inherit;
  font-weight: inherit;

  /* 确保边界处理 */
  border-radius: 0;
}

/* 主题变量 - 简单明显的高亮效果 */
:root {
  /* 亮色主题 - 明显的黄色高亮 */
  --highlight-bg: rgba(255, 235, 59, 0.6);
  --highlight-border: rgb(255, 193, 7);
  --highlight-text: rgb(33, 33, 33);
}

[data-theme='dark'] {
  /* 暗色主题 - 明显的黄色高亮 */
  --highlight-bg: rgba(255, 235, 59, 0.4);
  --highlight-border: rgb(255, 235, 59);
  --highlight-text: rgb(33, 33, 33);
}

[data-theme='sepia'] {
  /* 护眼主题 - 明显的黄色高亮 */
  --highlight-bg: rgba(255, 235, 59, 0.5);
  --highlight-border: rgb(255, 193, 7);
  --highlight-text: rgb(62, 39, 35);
}
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 阅读器主题样式 */
.prose-amber {
  --tw-prose-body: rgb(146 64 14);
  --tw-prose-headings: rgb(120 53 15);
  --tw-prose-lead: rgb(146 64 14);
  --tw-prose-links: rgb(217 119 6);
  --tw-prose-bold: rgb(120 53 15);
  --tw-prose-counters: rgb(146 64 14);
  --tw-prose-bullets: rgb(217 119 6);
  --tw-prose-hr: rgb(251 191 36);
  --tw-prose-quotes: rgb(120 53 15);
  --tw-prose-quote-borders: rgb(251 191 36);
  --tw-prose-captions: rgb(146 64 14);
  --tw-prose-code: rgb(120 53 15);
  --tw-prose-pre-code: rgb(251 191 36);
  --tw-prose-pre-bg: rgb(120 53 15);
  --tw-prose-th-borders: rgb(217 119 6);
  --tw-prose-td-borders: rgb(251 191 36);
}
