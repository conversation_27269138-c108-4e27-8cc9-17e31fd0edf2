# 跨行句子高亮修复说明

## 问题分析

用户反馈的跨行高亮问题：
1. **双重高亮**：句子跨行时出现两种颜色的高亮效果
2. **旧高亮残留**：旧的高亮没有被正确移除
3. **高亮位置错乱**：跨行句子的高亮从段落开头开始

## 根本原因

### 1. CSS跨行渲染问题
当内联元素（span）包含跨行文本时，CSS的背景、边框等属性会在每一行单独渲染，导致：
- 每一行都有独立的背景块
- 边框在每一行的开头都会出现
- 视觉上看起来像多个独立的高亮块

### 2. 重复的CSS规则
之前的CSS中有重复的`.sentence-highlight-inline`定义，导致样式冲突。

### 3. 不适合跨行的样式属性
使用了`border-left`、`padding`、`margin`等属性，这些在跨行时会造成视觉问题。

## 解决方案

### 1. 优化CSS高亮样式

**修复后的CSS：**
```css
/* 专为跨行优化的句子高亮样式 */
.sentence-highlight-inline {
  /* 使用mark标签的高亮方式，专门为跨行文本设计 */
  background: var(--highlight-bg);
  /* 关键：确保跨行时每行都有背景，但不重复 */
  box-decoration-break: clone;
  -webkit-box-decoration-break: clone;
  /* 不使用border和padding，避免跨行时的视觉问题 */
  border: none;
  padding: 0;
  margin: 0;
  /* 平滑过渡 */
  transition: background-color 0.2s ease-out, color 0.2s ease-out;
  /* 确保不影响行高和布局 */
  line-height: inherit;
  display: inline;
  /* 使用简单的圆角 */
  border-radius: 2px;
}
```

**关键改进：**
- 移除了所有可能在跨行时造成问题的属性（border、padding、margin）
- 使用`box-decoration-break: clone`确保跨行时背景连续
- 简化样式，专注于背景色高亮

### 2. 更换高亮颜色方案

**从蓝色改为黄色：**
```css
:root {
  /* 亮色主题 - 明显且跨行友好的高亮效果 */
  --highlight-bg: rgba(255, 235, 59, 0.4);
  --highlight-border: rgb(255, 193, 7);
  --highlight-text: rgb(33, 33, 33);
}
```

**优势：**
- 黄色高亮是传统的文本高亮颜色，用户更熟悉
- 在跨行时视觉连续性更好
- 在所有主题下都有良好的对比度

### 3. 简化浮动高亮

**OptimizedReadingArea.tsx的浮动高亮：**
```jsx
<div
  ref={sentenceHighlightRef}
  className="absolute floating-sentence-highlight"
  style={{
    opacity: 0,
    background: 'var(--highlight-bg)',
    border: 'none',
    padding: 0,
    margin: 0,
    borderRadius: '2px',
    transition: 'all 0.2s ease-out',
  }}
/>
```

**改进：**
- 移除了边框和内边距
- 使用与内联高亮相同的样式
- 避免了浮动高亮与内联高亮的样式不一致

## 技术原理

### 1. box-decoration-break属性
```css
box-decoration-break: clone;
-webkit-box-decoration-break: clone;
```
这个属性确保当内联元素跨行时：
- 每一行都有完整的背景
- 背景在行与行之间连续
- 不会出现断裂或重复

### 2. 简化的样式策略
- **只使用背景色**：避免边框、阴影等复杂装饰
- **最小化布局影响**：不使用padding、margin
- **保持文字清晰**：使用高对比度的文字颜色

### 3. 主题适配
- **亮色主题**：深色文字 + 黄色背景
- **暗色主题**：白色文字 + 黄色背景
- **护眼主题**：深棕色文字 + 黄色背景

## 修复效果

### 修复前的问题
- ❌ 跨行句子出现多个高亮块
- ❌ 高亮颜色不够明显
- ❌ 边框在每行开头重复出现
- ❌ 视觉上看起来像多个独立的高亮

### 修复后的效果
- ✅ 跨行句子有连续的高亮背景
- ✅ 高亮颜色明显，黄色背景清晰可见
- ✅ 没有多余的边框或装饰
- ✅ 视觉上是一个完整的句子高亮

## 测试建议

使用包含长句子的文本进行测试：
1. 创建包含跨行句子的测试文本
2. 启动朗读功能
3. 观察跨行句子的高亮效果
4. 确认没有重复高亮或颜色冲突

## 兼容性

- **浏览器兼容性**：`box-decoration-break`在现代浏览器中支持良好
- **主题兼容性**：在所有三种主题下都有良好的视觉效果
- **功能兼容性**：不影响现有的朗读和导航功能
